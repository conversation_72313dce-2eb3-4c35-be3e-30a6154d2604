# OMOP ETL Pipeline Configuration - Comprehensive E2E Test Version
# This configuration file defines the mappings from source data to OMOP CDM tables
# Enhanced for comprehensive end-to-end testing with all major OMOP tables

# Database connections
source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_clinical_data
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    connect_timeout: 30
    application_name: omop_etl_e2e_test

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_cdm
  username: test_user
  password: test_password
  parameters:
    sslmode: disable
    schema: test_cdm
    application_name: omop_etl_e2e_test

# ETL settings
etl_settings:
  batch_size: 100  # Smaller for testing
  parallel_workers: 2
  validation_mode: strict  # strict, warning, or skip
  error_threshold: 0.05    # Allow 5% error rate for testing
  checkpoint_enabled: true
  checkpoint_interval: 60   # seconds - shorter for testing
  log_level: debug
  enable_profiling: true
  enable_monitoring: true
  max_retry_attempts: 3
  retry_delay_seconds: 5
  continue_on_error: false
  enable_data_quality_checks: true

# Vocabulary mappings
vocabulary_mappings:
  Gender:
    "Male": 8507
    "M": 8507
    "MALE": 8507
    "Female": 8532
    "F": 8532
    "FEMALE": 8532
    "Unknown": 0
    "U": 0

  Race:
    "White": 8527
    "Caucasian": 8527
    "Black or African American": 8516
    "Black": 8516
    "Asian": 8515
    "American Indian or Alaska Native": 8657
    "Native Hawaiian or Other Pacific Islander": 8557
    "Unknown": 0

  Ethnicity:
    "Hispanic or Latino": 38003563
    "Hispanic": 38003563
    "Not Hispanic or Latino": 38003564
    "Non-Hispanic": 38003564
    "Unknown": 0

  VisitType:
    "Inpatient": 9201
    "IP": 9201
    "Outpatient": 9202
    "OP": 9202
    "Emergency": 9203
    "ER": 9203
    "ED": 9203

# Table mappings
table_mappings:
  # Person table mapping
  person:
    source_table: patients
    target_table: person
    pre_process_sql: |
      -- Clean up birth dates
      UPDATE patients
      SET birth_date = NULL
      WHERE birth_date < '1900-01-01' OR birth_date > CURRENT_DATE
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true
        default_time: "00:00:00"

      - source_column: birth_date
        target_column: year_of_birth
        type: custom
        function: extract_year

      - source_column: birth_date
        target_column: month_of_birth
        type: custom
        function: extract_month

      - source_column: birth_date
        target_column: day_of_birth
        type: custom
        function: extract_day

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        default_value: 0

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default_value: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default_value: 0

      - source_column: patient_id
        target_column: person_source_value
        type: direct

      - source_column: gender
        target_column: gender_source_value
        type: direct

      - source_column: race
        target_column: race_source_value
        type: direct

      - source_column: ethnicity
        target_column: ethnicity_source_value
        type: direct

    filters:
      - field: birth_date
        operator: not_null
      - field: gender
        operator: not_null

    validations:
      - field: year_of_birth
        rule: range
        min: 1900
        max: 2024
      - field: gender_concept_id
        rule: not_zero
      - field: person_id
        rule: unique

  # Visit Occurrence mapping
  visit_occurrence:
    source_table: encounters
    target_table: visit_occurrence
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: encounter_type
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: VisitType
        default_value: 0

      - source_column: admission_date
        target_column: visit_start_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: discharge_date
        target_column: visit_end_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: admission_date
        target_column: visit_start_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: discharge_date
        target_column: visit_end_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: visit_type_source
        target_column: visit_type_concept_id
        type: conditional
        conditions:
          - if: visit_type_source == "EHR"
            then: 32817  # EHR encounter
          - if: visit_type_source == "CLAIM"
            then: 32810  # Claim
          - else: 0

      - source_column: encounter_id
        target_column: visit_source_value
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

      - source_column: department_id
        target_column: care_site_id
        type: direct

    filters:
      - field: admission_date
        operator: not_null
      - field: patient_id
        operator: exists
        reference_table: person
        reference_field: person_id

    validations:
      - field: visit_start_date
        rule: date_range
        min: "1900-01-01"
        max: "2099-12-31"
      - field: visit_end_date
        rule: greater_than_or_equal
        compare_field: visit_start_date

  # Condition Occurrence mapping
  condition_occurrence:
    source_table: diagnoses
    target_table: condition_occurrence
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: icd_code
        target_column: condition_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM
        target_vocabulary: SNOMED
        mapping_type: source_to_standard

      - source_column: diagnosis_date
        target_column: condition_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: diagnosis_date
        target_column: condition_start_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true

      - source_column: diagnosis_type
        target_column: condition_type_concept_id
        type: conditional
        conditions:
          - if: diagnosis_type == "PRIMARY"
            then: 32902  # Primary diagnosis
          - if: diagnosis_type == "SECONDARY"
            then: 32908  # Secondary diagnosis
          - if: diagnosis_type == "ADMISSION"
            then: 32901  # Admission diagnosis
          - else: 32899  # Other diagnosis

      - source_column: icd_code
        target_column: condition_source_value
        type: direct

      - source_column: icd_code
        target_column: condition_source_concept_id
        type: vocabulary_mapping
        vocabulary: ICD10CM

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: provider_id
        target_column: provider_id
        type: direct

    validations:
      - field: condition_concept_id
        rule: not_zero
      - field: person_id
        rule: exists
        reference_table: person
        reference_field: person_id

  # Drug Exposure mapping
  drug_exposure:
    source_table: medications
    target_table: drug_exposure
    transformations:
      - source_column: medication_id
        target_column: drug_exposure_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: drug_code
        target_column: drug_concept_id
        type: vocabulary_mapping
        vocabulary: RxNorm
        mapping_type: ingredient_level

      - source_column: start_date
        target_column: drug_exposure_start_date
        type: date_transform
        format: "%Y-%m-%d"

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_date
        type: date_calculation
        calculation: start_date + duration_days

      - source_column: start_date
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "%Y-%m-%d"
        add_time: true

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_datetime
        type: date_calculation
        calculation: start_date + duration_days
        add_time: true

      - source_column: prescription_type
        target_column: drug_type_concept_id
        type: conditional
        conditions:
          - if: prescription_type == "PRESCRIPTION"
            then: 38000177  # Prescription written
          - if: prescription_type == "DISPENSED"
            then: 38000175  # Prescription dispensed
          - if: prescription_type == "ADMINISTERED"
            then: 38000180  # Inpatient administration
          - else: 0

      - source_column: quantity
        target_column: quantity
        type: numeric_transform
        operation: round
        precision: 2

      - source_column: days_supply
        target_column: days_supply
        type: direct

      - source_column: refills
        target_column: refills
        type: direct

      - source_column: sig_text
        target_column: sig
        type: string_transform
        max_length: 500

      - source_column: route
        target_column: route_concept_id
        type: vocabulary_mapping
        vocabulary: Route

      - source_column: drug_name
        target_column: drug_source_value
        type: direct

      - source_column: route
        target_column: route_source_value
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

    validations:
      - field: drug_concept_id
        rule: not_zero
      - field: drug_exposure_start_date
        rule: not_null
      - field: quantity
        rule: positive_number

  # Measurement mapping
  measurement:
    source_table: lab_results
    target_table: measurement
    transformations:
      - source_column: lab_id
        target_column: measurement_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: loinc_code
        target_column: measurement_concept_id
        type: vocabulary_mapping
        vocabulary: LOINC

      - source_column: result_date
        target_column: measurement_date
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_date
        target_column: measurement_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_type
        target_column: measurement_type_concept_id
        type: conditional
        conditions:
          - if: result_type == "LAB"
            then: 44818702  # Lab result
          - if: result_type == "VITAL"
            then: 44818701  # From physical examination
          - else: 0

      - source_column: operator
        target_column: operator_concept_id
        type: vocabulary_mapping
        vocabulary: MeasurementOperator

      - source_column: numeric_result
        target_column: value_as_number
        type: numeric_transform

      - source_column: text_result
        target_column: value_as_concept_id
        type: conditional
        conditions:
          - if: text_result == "POSITIVE"
            then: 45884084  # Positive
          - if: text_result == "NEGATIVE"
            then: 45878583  # Negative
          - if: text_result == "ABNORMAL"
            then: 45878745  # Abnormal
          - else: 0

      - source_column: unit
        target_column: unit_concept_id
        type: vocabulary_mapping
        vocabulary: Unit

      - source_column: ref_range_low
        target_column: range_low
        type: numeric_transform

      - source_column: ref_range_high
        target_column: range_high
        type: numeric_transform

      - source_column: lab_name
        target_column: measurement_source_value
        type: direct

      - source_column: unit
        target_column: unit_source_value
        type: direct

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

    validations:
      - field: measurement_concept_id
        rule: not_zero
      - field: value_as_number
        rule: reasonable_range
        context: measurement_concept_id

# Custom transformation functions
custom_functions:
  extract_year:
    type: sql
    expression: "EXTRACT(YEAR FROM {field})"

  extract_month:
    type: sql
    expression: "EXTRACT(MONTH FROM {field})"

  extract_day:
    type: sql
    expression: "EXTRACT(DAY FROM {field})"

# Data quality checks
quality_checks:
  - name: person_count_check
    description: Verify person count matches source
    query: |
      SELECT COUNT(*) as target_count,
             (SELECT COUNT(*) FROM source.patients) as source_count
      FROM cdm.person
    expected: target_count = source_count

  - name: orphan_visits_check
    description: Check for visits without valid person
    query: |
      SELECT COUNT(*) as orphan_count
      FROM cdm.visit_occurrence v
      LEFT JOIN cdm.person p ON v.person_id = p.person_id
      WHERE p.person_id IS NULL
    expected: orphan_count = 0

  - name: date_consistency_check
    description: Verify dates are logically consistent
    query: |
      SELECT COUNT(*) as invalid_dates
      FROM cdm.visit_occurrence
      WHERE visit_start_date > visit_end_date
    expected: invalid_dates = 0