# Dockerfile for OMOP ETL Pipeline
# Supports all build targets through parameterization

# Build arguments
ARG BUILD_TARGET=api
ARG BUILD_TYPE=release
ARG ENABLE_TESTS=false
ARG ENABLE_DEV_TOOLS=false
ARG ENABLE_GRPC=false
ARG ENABLE_REST_API=false
ARG TARGETPLATFORM
ARG TARGETARCH
ARG TARGETOS

# Multi-stage build
FROM ubuntu:22.04 AS base

# Set environment variables for non-interactive installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set architecture-specific variables
ENV TARGETPLATFORM=${TARGETPLATFORM}
ENV TARGETARCH=${TARGETARCH}
ENV TARGETOS=${TARGETOS}

# Add GCC 13 repository for better C++20 support
RUN apt-get update && apt-get install -y software-properties-common && \
    add-apt-repository ppa:ubuntu-toolchain-r/test && \
    apt-get update

# Install base dependencies
RUN apt-get install -y \
    gcc-13 \
    g++-13 \
    build-essential \
    git \
    pkg-config \
    ninja-build \
    libpq-dev \
    unixodbc-dev \
    libyaml-cpp-dev \
    nlohmann-json3-dev \
    libspdlog-dev \
    libfmt-dev \
    libssl-dev \
    uuid-dev \
    zlib1g-dev \
    wget \
    curl \
    libcurl4-openssl-dev \
    unzip \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Set GCC 13 as default
RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 100 && \
    update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 100

# Conditional installation of development tools
ARG ENABLE_DEV_TOOLS
RUN if [ "$ENABLE_DEV_TOOLS" = "true" ]; then \
        apt-get update && apt-get install -y \
        gdb \
        valgrind \
        clang \
        clang-tools \
        clang-format \
        clang-tidy \
        cppcheck \
        vim \
        nano \
        htop \
        tree \
        jq \
        python3 \
        python3-pip \
        && rm -rf /var/lib/apt/lists/* && \
        pip3 install conan==2.0.* pre-commit; \
    fi

# Conditional installation of test dependencies
ARG ENABLE_TESTS
RUN if [ "$ENABLE_TESTS" = "true" ]; then \
        apt-get update && apt-get install -y \
        libgtest-dev \
        libgmock-dev \
        && rm -rf /var/lib/apt/lists/*; \
    fi

# Conditional installation of database dependencies
ARG BUILD_TARGET
RUN if [ "$BUILD_TARGET" = "api" ] || [ "$BUILD_TARGET" = "service" ] || [ "$BUILD_TARGET" = "extract" ] || [ "$BUILD_TARGET" = "load" ]; then \
        apt-get update && apt-get install -y \
        libmysqlclient-dev \
        && rm -rf /var/lib/apt/lists/*; \
    fi

# Conditional installation of gRPC dependencies
ARG ENABLE_GRPC
RUN if [ "$ENABLE_GRPC" = "true" ]; then \
        apt-get update && apt-get install -y \
        libgrpc++-dev \
        libprotobuf-dev \
        protobuf-compiler-grpc \
        && rm -rf /var/lib/apt/lists/*; \
    fi

# Conditional installation of REST API dependencies
ARG ENABLE_REST_API
RUN if [ "$ENABLE_REST_API" = "true" ]; then \
        apt-get update && apt-get install -y \
        libcpprest-dev \
        && rm -rf /var/lib/apt/lists/*; \
    fi

# Conditional installation of additional dependencies for specific targets
RUN if [ "$BUILD_TARGET" = "load" ] || [ "$BUILD_TARGET" = "extract" ]; then \
        apt-get update && apt-get install -y \
        libarchive-dev \
        && rm -rf /var/lib/apt/lists/*; \
    fi

# Install newer CMake (multi-architecture support)
RUN echo "Target platform: ${TARGETPLATFORM:-unknown}" && \
    echo "Target architecture: ${TARGETARCH:-unknown}" && \
    ARCH=$(uname -m) && \
    echo "Detected runtime architecture: $ARCH" && \
    # Map Docker TARGETARCH to CMake architecture names
    case "${TARGETARCH:-$ARCH}" in \
        "amd64"|"x86_64") CMAKE_ARCH="x86_64" ;; \
        "arm64"|"aarch64") CMAKE_ARCH="aarch64" ;; \
        "arm/v7"|"armhf") CMAKE_ARCH="armhf" ;; \
        *) \
            echo "Unsupported architecture: ${TARGETARCH:-$ARCH}"; \
            echo "Falling back to system architecture detection"; \
            case "$ARCH" in \
                "x86_64") CMAKE_ARCH="x86_64" ;; \
                "aarch64") CMAKE_ARCH="aarch64" ;; \
                "armv7l") CMAKE_ARCH="armhf" ;; \
                *) echo "ERROR: Unsupported architecture $ARCH" && exit 1 ;; \
            esac ;; \
    esac && \
    echo "Using CMake architecture: $CMAKE_ARCH" && \
    # Download and install CMake
    CMAKE_VERSION="3.28.1" && \
    CMAKE_URL="https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" && \
    echo "Downloading CMake from: $CMAKE_URL" && \
    wget "$CMAKE_URL" && \
    tar -xzf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" && \
    cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/bin/"* /usr/local/bin/ && \
    cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/share/"* /usr/local/share/ && \
    rm -rf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}"* && \
    # Verify installation
    cmake --version

# Install cpp-httplib (header-only)
RUN wget https://github.com/yhirose/cpp-httplib/archive/refs/tags/v0.14.0.tar.gz && \
    tar -xzf v0.14.0.tar.gz && \
    cp cpp-httplib-0.14.0/httplib.h /usr/local/include/ && \
    rm -rf cpp-httplib-0.14.0 v0.14.0.tar.gz

# Create development user (only if dev tools are enabled)
ARG ENABLE_DEV_TOOLS
RUN if [ "$ENABLE_DEV_TOOLS" = "true" ]; then \
        useradd -m -s /bin/bash dev && \
        usermod -aG sudo dev && \
        echo "dev ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers; \
    fi

# Builder stage
FROM base AS builder

WORKDIR /build
COPY . .

# Set build arguments for CMake configuration
ARG BUILD_TARGET
ARG BUILD_TYPE
ARG ENABLE_TESTS
ARG ENABLE_GRPC
ARG ENABLE_REST_API

# Configure CMake based on build parameters
RUN CMAKE_BUILD_TYPE=$(echo "$BUILD_TYPE" | tr '[:lower:]' '[:upper:]') && \
    CMAKE_ARGS="-DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE -GNinja" && \
    if [ "$ENABLE_TESTS" = "true" ]; then \
        CMAKE_ARGS="$CMAKE_ARGS -DBUILD_TESTING=ON"; \
    else \
        CMAKE_ARGS="$CMAKE_ARGS -DBUILD_TESTING=OFF"; \
    fi && \
    if [ "$ENABLE_GRPC" = "true" ]; then \
        CMAKE_ARGS="$CMAKE_ARGS -DENABLE_GRPC=ON"; \
    fi && \
    if [ "$ENABLE_REST_API" = "true" ]; then \
        CMAKE_ARGS="$CMAKE_ARGS -DENABLE_REST_API=ON"; \
    fi && \
    CMAKE_ARGS="$CMAKE_ARGS -DYAML_CPP_BUILD_TESTS=OFF" && \
    echo "CMake configuration: cmake -B build -S . $CMAKE_ARGS" && \
    cmake -B build -S . $CMAKE_ARGS

# Build dependencies based on target
RUN echo "Building target: $BUILD_TARGET" && \
    case "$BUILD_TARGET" in \
        "common") \
            cmake --build build --target omop_common -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found"; \
                cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found"; \
            fi ;; \
        "core") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found"; \
                cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found"; \
            fi ;; \
        "cdm") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_cdm -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found"; \
                cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found"; \
            fi ;; \
        "extract") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_extract -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found"; \
                cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found"; \
            fi ;; \
        "transform") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_transform -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found"; \
                cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found"; \
            fi ;; \
        "load") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_load -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found"; \
                cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found"; \
            fi ;; \
        "service") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_extract -j$(nproc) && \
            cmake --build build --target omop_transform -j$(nproc) && \
            cmake --build build --target omop_load -j$(nproc) && \
            cmake --build build --target omop_service -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found"; \
                cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found"; \
            fi ;; \
        "api") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_extract -j$(nproc) && \
            cmake --build build --target omop_transform -j$(nproc) && \
            cmake --build build --target omop_load -j$(nproc) && \
            cmake --build build --target omop_service -j$(nproc) && \
            cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" && \
            cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found"; \
                cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found"; \
            fi ;; \
        "cli") \
            cmake --build build --target omop_common -j$(nproc) && \
            cmake --build build --target omop_core -j$(nproc) && \
            cmake --build build --target omop_extract -j$(nproc) && \
            cmake --build build --target omop_transform -j$(nproc) && \
            cmake --build build --target omop_load -j$(nproc) && \
            cmake --build build --target omop_service -j$(nproc) && \
            cmake --build build --target omop_cli -j$(nproc) && \
            if [ "$ENABLE_TESTS" = "true" ]; then \
                cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found"; \
                cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found"; \
            fi ;; \
        "all") \
            cmake --build build -j$(nproc) ;; \
        *) \
            echo "Unknown build target: $BUILD_TARGET" && exit 1 ;; \
    esac

# Run tests if enabled
ARG ENABLE_TESTS
ARG BUILD_TARGET
RUN if [ "$ENABLE_TESTS" = "true" ]; then \
        cd build && \
        case "$BUILD_TARGET" in \
            "common") ctest -R "common|Common" --output-on-failure || echo "Common tests completed" ;; \
            "core") ctest -R "core|Core" --output-on-failure || echo "Core tests completed" ;; \
            "cdm") ctest -R "cdm|CDM" --output-on-failure || echo "CDM tests completed" ;; \
            "extract") ctest -R "extract|Extract" --output-on-failure || echo "Extract tests completed" ;; \
            "transform") ctest -R "transform|Transform" --output-on-failure || echo "Transform tests completed" ;; \
            "load") ctest -R "load|Load" --output-on-failure || echo "Load tests completed" ;; \
            "service") ctest -R "service|Service" --output-on-failure || echo "Service tests completed" ;; \
            "api") ctest -R "api|API" --output-on-failure || echo "API tests completed" ;; \
            "cli") ctest -R "cli|CLI" --output-on-failure || echo "CLI tests completed" ;; \
            "all") ctest --output-on-failure || echo "All tests completed" ;; \
        esac; \
    fi

# Create bin directory and marker files for runtime stage
RUN echo "BUILD_TARGET=$BUILD_TARGET" > /build/.build_info && \
    echo "BUILD_TYPE=$BUILD_TYPE" >> /build/.build_info && \
    echo "BUILD_DIR=/build/build" >> /build/.build_info && \
    mkdir -p /build/build/bin && \
    case "$BUILD_TARGET" in \
        "api"|"cli"|"service") \
            echo "Executable build target - checking for binaries" && \
            ls -la /build/build/bin/ || echo "No binaries found yet" ;; \
        *) \
            echo "Library-only build target - creating empty bin directory" && \
            touch /build/build/bin/.keep ;; \
    esac

# Development stage (for development containers)
FROM base AS development

ARG ENABLE_DEV_TOOLS
RUN if [ "$ENABLE_DEV_TOOLS" != "true" ]; then \
        echo "Development stage requires ENABLE_DEV_TOOLS=true" && exit 1; \
    fi

# Set up development environment
USER dev
WORKDIR /home/<USER>

# Initialize Conan
RUN conan profile detect --force

# Set working directory
WORKDIR /workspace

# Default command for development
CMD ["/bin/bash"]

# Runtime stage
FROM ubuntu:22.04 AS runtime

# Set environment variables for non-interactive installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    unixodbc \
    libyaml-cpp0.7 \
    libspdlog1 \
    libfmt8 \
    libssl3 \
    uuid-runtime \
    ca-certificates \
    curl \
    libcurl4 \
    && rm -rf /var/lib/apt/lists/*

# Create user and directories
RUN useradd -m -s /bin/bash omop && \
    mkdir -p /etc/omop-etl /var/log/omop-etl /var/lib/omop-etl/data && \
    chown -R omop:omop /etc/omop-etl /var/log/omop-etl /var/lib/omop-etl

# Copy build info and artifacts from builder stage
COPY --from=builder /build/.build_info /tmp/.build_info

# Copy built artifacts based on build target
ARG BUILD_TARGET
RUN BUILD_TARGET=$(grep BUILD_TARGET /tmp/.build_info | cut -d'=' -f2) && \
    mkdir -p /usr/local/lib /usr/local/bin && \
    echo "Setting up runtime for target: $BUILD_TARGET"

# Copy libraries and binaries from builder  
COPY --from=builder /build/build/lib/ /usr/local/lib/
COPY --from=builder /build/build/bin/ /usr/local/bin/

# Copy configuration templates
COPY --chown=omop:omop config /etc/omop-etl/templates

# Set environment variables
ENV LD_LIBRARY_PATH=/usr/local/lib
ENV OMOP_CONFIG_PATH=/etc/omop-etl
ENV OMOP_LOG_PATH=/var/log/omop-etl
ENV OMOP_DATA_PATH=/var/lib/omop-etl/data

# Set default command based on build target (before switching user)
ARG BUILD_TARGET
RUN BUILD_TARGET=$(grep BUILD_TARGET /tmp/.build_info | cut -d'=' -f2) && \
    case "$BUILD_TARGET" in \
        "api") echo '#!/bin/bash\nexec /usr/local/bin/omop_api_service "$@"' > /usr/local/bin/entrypoint.sh ;; \
        "cli") echo '#!/bin/bash\nexec /usr/local/bin/omop_cli "$@"' > /usr/local/bin/entrypoint.sh ;; \
        *) echo '#!/bin/bash\ntail -f /dev/null' > /usr/local/bin/entrypoint.sh ;; \
    esac && \
    chmod +x /usr/local/bin/entrypoint.sh

# Switch to non-root user
USER omop
WORKDIR /home/<USER>

# Expose default ports
EXPOSE 8080

CMD ["/usr/local/bin/entrypoint.sh"] 