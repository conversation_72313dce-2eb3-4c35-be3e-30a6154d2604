--- a/src/lib/extract/extract_utils.h
+++ b/src/lib/extract/extract_utils.h
@@ -190,7 +190,7 @@ public:
      * @brief Configuration for parallel extraction
      */
     struct Config {
-        size_t num_threads = 4;
+        size_t num_workers = 4;
         size_t queue_size = 100;
         bool preserve_order = false;
     };
@@ -234,6 +234,18 @@ public:
     std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
     get_all_statistics() const;
 
+    /**
+     * @brief Add task to execution queue
+     * @param task Task function
+     */
+    void add_task(std::function<void()> task);
+
+    /**
+     * @brief Wait for all tasks to complete
+     */
+    void wait_for_completion();
+
+
 private:
     Config config_;
     std::vector<std::thread> workers_;