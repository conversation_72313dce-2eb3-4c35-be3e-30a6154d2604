#pragma once

#include <memory>
#include <string>
#include <vector>
#include <optional>
#include <any>
#include <functional>
#include <format>
#include <unordered_map>
#include <random>
#include <chrono>
#include <thread>
#include <condition_variable>
#include <queue>
#include <format>
#include "core/interfaces.h"
#include "common/exceptions.h"

namespace omop::extract {

/**
 * @brief Result set interface for database queries
 *
 * This interface provides access to query results in a database-agnostic manner.
 * Implementations handle the specifics of each database system.
 */
class IResultSet {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IResultSet() = default;

    /**
     * @brief Move to next row
     * @return bool True if successful, false if no more rows
     */
    virtual bool next() = 0;

    /**
     * @brief Get column value by index
     * @param index Column index (0-based)
     * @return std::any Column value
     */
    virtual std::any get_value(size_t index) const = 0;

    /**
     * @brief Get column value by name
     * @param column_name Column name
     * @return std::any Column value
     */
    virtual std::any get_value(const std::string& column_name) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param index Column index
     * @return bool True if NULL
     */
    virtual bool is_null(size_t index) const = 0;

    /**
     * @brief Check if column value is NULL
     * @param column_name Column name
     * @return bool True if NULL
     */
    virtual bool is_null(const std::string& column_name) const = 0;

    /**
     * @brief Get column count
     * @return size_t Number of columns
     */
    virtual size_t column_count() const = 0;

    /**
     * @brief Get column name by index
     * @param index Column index
     * @return std::string Column name
     */
    virtual std::string column_name(size_t index) const = 0;

    /**
     * @brief Get column type by index
     * @param index Column index
     * @return std::string Column type name
     */
    virtual std::string column_type(size_t index) const = 0;

    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation of current row
     */
    virtual core::Record to_record() const = 0;
};

/**
 * @brief Prepared statement interface
 *
 * This interface provides parameterized query execution capabilities
 * for safe and efficient database operations.
 */
class IPreparedStatement {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IPreparedStatement() = default;

    /**
     * @brief Bind parameter by index
     * @param index Parameter index (1-based)
     * @param value Parameter value
     */
    virtual void bind(size_t index, const std::any& value) = 0;

    /**
     * @brief Execute query and return result set
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query() = 0;

    /**
     * @brief Execute update/insert/delete
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update() = 0;

    /**
     * @brief Clear all bound parameters
     */
    virtual void clear_parameters() = 0;
};

/**
 * @brief Database connection interface
 *
 * This interface defines the contract for database connections,
 * providing a unified API for different database systems.
 */
class IDatabaseConnection {
public:
    /**
     * @brief Connection parameters
     */
    struct ConnectionParams {
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
        std::unordered_map<std::string, std::string> options;
    };

    /**
     * @brief Virtual destructor
     */
    virtual ~IDatabaseConnection() = default;

    /**
     * @brief Connect to database
     * @param params Connection parameters
     */
    virtual void connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from database
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Execute SQL query
     * @param sql SQL query string
     * @return std::unique_ptr<IResultSet> Query results
     */
    virtual std::unique_ptr<IResultSet> execute_query(const std::string& sql) = 0;

    /**
     * @brief Execute SQL update/insert/delete
     * @param sql SQL statement
     * @return size_t Number of affected rows
     */
    virtual size_t execute_update(const std::string& sql) = 0;

    /**
     * @brief Prepare SQL statement
     * @param sql SQL statement with parameter placeholders
     * @return std::unique_ptr<IPreparedStatement> Prepared statement
     */
    virtual std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) = 0;

    /**
     * @brief Begin transaction
     */
    virtual void begin_transaction() = 0;

    /**
     * @brief Commit transaction
     */
    virtual void commit() = 0;

    /**
     * @brief Rollback transaction
     */
    virtual void rollback() = 0;

    /**
     * @brief Get database type name
     * @return std::string Database type
     */
    virtual std::string get_database_type() const = 0;

    /**
     * @brief Get database version
     * @return std::string Database version string
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief Set query timeout
     * @param seconds Timeout in seconds
     */
    virtual void set_query_timeout(int seconds) = 0;

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return bool True if table exists
     */
    virtual bool table_exists(const std::string& table_name,
                            const std::string& schema = "") const = 0;

    /**
     * @brief Check if in transaction
     * @return bool True if in transaction
     */
    virtual bool in_transaction() const = 0;
};

/**
 * @brief Base implementation of IResultSet with common functionality
 */
class ResultSetBase : public IResultSet {
public:
    /**
     * @brief Convert current row to Record
     * @return core::Record Record representation
     */
    core::Record to_record() const override {
        core::Record record;

        for (size_t i = 0; i < column_count(); ++i) {
            if (!is_null(i)) {
                std::string col_name = column_name(i);
                record.setField(col_name, get_value(i));
            }
        }

        return record;
    }
};

/**
 * @brief Database extractor implementation
 *
 * This class implements the IExtractor interface for database sources,
 * providing efficient batch extraction from SQL databases.
 */
class DatabaseExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     */
    explicit DatabaseExtractor(std::unique_ptr<IDatabaseConnection> connection)
        : connection_(std::move(connection)) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override { return has_more_data_; }

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Build extraction query
     * @return std::string SQL query
     */
    virtual std::string build_query() const;

    /**
     * @brief Apply filters to query
     * @param base_query Base SQL query
     * @return std::string Query with filters applied
     */
    virtual std::string apply_filters(const std::string& base_query) const;

protected:
    std::unique_ptr<IDatabaseConnection> connection_;
    std::unique_ptr<IResultSet> current_result_set_;
    std::string table_name_;
    std::string schema_name_;
    std::vector<std::string> columns_;
    std::string filter_condition_;
    std::string order_by_;
    bool has_more_data_{true};
    size_t total_extracted_{0};
    size_t batch_count_{0};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Connection pool for database connections
 *
 * This class manages a pool of database connections for improved performance
 * and resource management in multi-threaded environments.
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param min_connections Minimum number of connections
     * @param max_connections Maximum number of connections
     * @param connection_factory Factory function for creating connections
     */
    ConnectionPool(size_t min_connections,
                  size_t max_connections,
                  std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory);

    /**
     * @brief Virtual destructor
     */
    virtual ~ConnectionPool();

    /**
     * @brief Acquire connection from pool
     * @param timeout_ms Timeout in milliseconds
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    std::unique_ptr<IDatabaseConnection> acquire(int timeout_ms = -1);

    /**
     * @brief Return connection to pool
     * @param connection Connection to return
     */
    void release(std::unique_ptr<IDatabaseConnection> connection);

    /**
     * @brief Get pool statistics
     * @return Statistics including active connections, idle connections, etc.
     */
    struct PoolStats {
        size_t total_connections;
        size_t active_connections;
        size_t idle_connections;
        size_t total_acquisitions;
        size_t total_releases;
        size_t wait_count;
        std::chrono::milliseconds avg_wait_time;
    };

    [[nodiscard]] PoolStats get_statistics() const;

    /**
     * @brief Clear all idle connections
     */
    void clear_idle_connections();

    /**
     * @brief Validate all connections
     * @return size_t Number of invalid connections removed
     */
    size_t validate_connections();

private:
    size_t min_connections_;
    size_t max_connections_;
    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
    
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::queue<std::unique_ptr<IDatabaseConnection>> idle_connections_;
    size_t active_connections_{0};
    bool shutdown_{false};
    
    // Statistics
    std::atomic<size_t> total_acquisitions_;
    std::atomic<size_t> total_releases_;
    std::atomic<size_t> wait_count_;
    std::atomic<int64_t> total_wait_time_; // milliseconds
};

/**
 * @brief Factory for creating database connections
 */
class DatabaseConnectionFactory {
public:
    using Creator = std::function<std::unique_ptr<IDatabaseConnection>(const IDatabaseConnection::ConnectionParams&)>;

    /**
     * @brief Get factory instance
     * @return DatabaseConnectionFactory& Singleton instance
     */
    static DatabaseConnectionFactory& instance() {
        static DatabaseConnectionFactory instance;
        return instance;
    }

    /**
     * @brief Register database type
     * @param type Database type name
     * @param creator Creator function
     */
    void register_type(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create connection by type
     * @param type Database type
     * @param params Connection parameters
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create(const std::string& type, const IDatabaseConnection::ConnectionParams& params) {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second(params);
        }
        throw common::DatabaseException(
            std::format("Unknown database type: '{}'", type), type, 0);
    }

    /**
     * @brief Create connection from configuration
     * @param config Database configuration
     * @return std::unique_ptr<IDatabaseConnection> Database connection
     */
    [[nodiscard]] std::unique_ptr<IDatabaseConnection> create_from_config(
        const std::unordered_map<std::string, std::any>& config);

private:
    DatabaseConnectionFactory() = default;
    std::unordered_map<std::string, Creator> creators_;
};

/**
 * @brief Connection retry policy with exponential backoff
 */
class ConnectionRetryPolicy {
public:
    struct Config {
        size_t max_attempts;
        std::chrono::milliseconds initial_delay;
        double backoff_multiplier;
        std::chrono::milliseconds max_delay;
        bool add_jitter;
        
        Config() : max_attempts(3), initial_delay(1000), backoff_multiplier(2.0), 
                   max_delay(30000), add_jitter(true) {}
    };

    explicit ConnectionRetryPolicy(const Config& config = Config{})
        : config_(config) {}

    /**
     * @brief Execute connection with retry logic
     * @param connection_func Function that attempts connection
     * @param logger Logger for retry messages
     * @return bool True if connection successful
     */
    template<typename ConnectionFunc>
    bool execute_with_retry(ConnectionFunc connection_func,
                          std::shared_ptr<common::Logger> logger) {
        size_t attempt = 0;
        std::chrono::milliseconds delay = config_.initial_delay;

        while (attempt < config_.max_attempts) {
            try {
                connection_func();
                return true;
            } catch (const std::exception& e) {
                attempt++;
                if (attempt >= config_.max_attempts) {
                    logger->error("Connection failed after {} attempts: {}",
                                config_.max_attempts, e.what());
                    throw;
                }

                // Add jitter to prevent thundering herd
                auto actual_delay = delay;
                if (config_.add_jitter) {
                    std::random_device rd;
                    std::mt19937 gen(rd());
                    std::uniform_int_distribution<> dis(0, delay.count() / 4);
                    actual_delay += std::chrono::milliseconds(dis(gen));
                }

                logger->warn("Connection attempt {} failed: {}. Retrying in {}ms...",
                           attempt, e.what(), actual_delay.count());

                std::this_thread::sleep_for(actual_delay);

                // Exponential backoff
                delay = std::chrono::milliseconds(
                    static_cast<long>(delay.count() * config_.backoff_multiplier));
                if (delay > config_.max_delay) {
                    delay = config_.max_delay;
                }
            }
        }
        return false;
    }

private:
    Config config_;
};

} // namespace omop::extract