--- a/src/lib/extract/postgresql_connector.cpp
+++ b/src/lib/extract/postgresql_connector.cpp
@@ -9,6 +9,7 @@
 #include <ctime>
 #include <sstream>
 #include <iomanip>
+#include <unordered_map>
 
 namespace {
     // PostgreSQL type OIDs
@@ -27,6 +28,17 @@ namespace {
 
 namespace omop::extract {
 
+// ResultSetBase implementation for get_column_names
+std::vector<std::string> ResultSetBase::get_column_names() const {
+    std::vector<std::string> names;
+    size_t count = column_count();
+    names.reserve(count);
+    for (size_t i = 0; i < count; ++i) {
+        names.push_back(column_name(i));
+    }
+    return names;
+}
+
 // PostgreSQL type names
 static const std::unordered_map<Oid, std::string> PG_TYPE_NAMES = {
     {16, "boolean"},