--- a/src/lib/extract/extract_utils.cpp
+++ b/src/lib/extract/extract_utils.cpp
@@ -348,7 +348,7 @@ ParallelExtractor::ParallelExtractor(const Config& config)
     logger->info("Initializing parallel extractor with {} workers", config.num_workers);
     
     // Start worker threads
-    for (size_t i = 0; i < config.num_workers; ++i) {
+    for (size_t i = 0; i < config.num_workers; ++i) {
         workers_.emplace_back(&ParallelExtractor::worker_thread, this);
     }
 }