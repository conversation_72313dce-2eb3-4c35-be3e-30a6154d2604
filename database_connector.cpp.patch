--- a/src/lib/extract/database_connector.cpp
+++ b/src/lib/extract/database_connector.cpp
@@ -115,13 +115,13 @@ core::RecordBatch DatabaseExtractor::extract_batch(size_t batch_size,
         while (records_extracted < batch_size && current_result_set_->next()) {
             core::Record record;
             
             // Extract all columns from the result set
-            for (const auto& column_name : current_result_set_->get_column_names()) {
+            for (size_t i = 0; i < current_result_set_->column_count(); ++i) {
+                std::string column_name = current_result_set_->column_name(i);
                 std::any value = current_result_set_->get_value(column_name);
-                record.fields[column_name] = value;
+                record.setField(column_name, value);
             }
             
-            batch.records.push_back(std::move(record));
+            batch.addRecord(std::move(record));
             records_extracted++;
         }
 
@@ -129,11 +129,11 @@ core::RecordBatch DatabaseExtractor::extract_batch(size_t batch_size,
         if (records_extracted < batch_size) {
             has_more_data_ = false;
             current_result_set_.reset();
-            logger->info("Extraction completed. Total records extracted: {}", total_records_extracted_);
+            logger->info("Extraction completed. Total records extracted: {}", total_extracted_);
         }
 
-        total_records_extracted_ += records_extracted;
+        total_extracted_ += records_extracted;
         logger->debug("Extracted batch of {} records", records_extracted);
 
     } catch (const std::exception& e) {
@@ -157,11 +157,11 @@ void DatabaseExtractor::finalize([[maybe_unused]] core::ProcessingContext& conte
         end_time - start_time_).count();
 
     logger->info("Database extraction completed: {} records in {} seconds ({:.2f} records/sec)",
-                total_records_extracted_, duration,
-                duration > 0 ? static_cast<double>(total_records_extracted_) / duration : 0.0);
+                total_extracted_, duration,
+                duration > 0 ? static_cast<double>(total_extracted_) / duration : 0.0);
 }
 
 std::unordered_map<std::string, std::any> DatabaseExtractor::get_statistics() const {
     std::unordered_map<std::string, std::any> stats;
 
-    stats["total_records"] = total_records_extracted_;
-    stats["records_extracted"] = total_records_extracted_;  // Add test-expected key
+    stats["total_records"] = total_extracted_;
+    stats["records_extracted"] = total_extracted_;  // Add test-expected key
     stats["batch_count"] = batch_count_;
     stats["table_name"] = table_name_;
@@ -198,15 +198,11 @@ std::string DatabaseExtractor::apply_filters(const std::string& base_query) cons
     }
 }
 
-bool DatabaseExtractor::has_more_data() const {
-    return has_more_data_;
-}
-
-size_t DatabaseExtractor::get_total_records_extracted() const {
-    return total_records_extracted_;
+size_t DatabaseExtractor::get_total_records_extracted() const {
+    return total_extracted_;
 }
 
-std::chrono::milliseconds DatabaseExtractor::get_processing_time() const {
+std::chrono::milliseconds DatabaseExtractor::get_processing_time() const {
     auto end_time = std::chrono::steady_clock::now();
     return std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
 }
@@ -272,10 +268,17 @@ ConnectionPool::ConnectionPool(size_t min_connections,
     for (size_t i = 0; i < min_connections; ++i) {
         try {
             auto connection = connection_factory_();
-            if (connection && connection->connect()) {
-                idle_connections_.push(std::move(connection));
-            } else {
-                logger->warn("Failed to create initial connection {}", i);
+            if (connection) {
+                // Check if connection is already connected
+                if (!connection->is_connected()) {
+                    // Connection needs parameters to connect, skip pre-population
+                    logger->debug("Skipping pre-population of connection {} - requires parameters", i);
+                    break;
+                } else {
+                    idle_connections_.push(std::move(connection));
+                }
+            } else {
+                logger->warn("Failed to create initial connection {}", i);
             }
         } catch (const std::exception& e) {
             logger->error("Error creating initial connection {}: {}", i, e.what());
@@ -326,10 +329,17 @@ std::unique_ptr<IDatabaseConnection> ConnectionPool::acquire(int timeout_ms) {
         if (active_connections_ + idle_connections_.size() < max_connections_) {
             try {
                 auto connection = connection_factory_();
-                if (connection && connection->connect()) {
-                    active_connections_++;
-                    
-                    logger->debug("Created new connection. Active: {}, Idle: {}", 
+                if (connection) {
+                    // Check if connection is already connected
+                    if (!connection->is_connected()) {
+                        // Can't create new connection without parameters
+                        logger->debug("Cannot create new connection - requires parameters");
+                    } else {
+                        active_connections_++;
+                        
+                        logger->debug("Created new connection. Active: {}, Idle: {}", 
+                                     active_connections_, idle_connections_.size());
+                        
+                        return connection;
+                    }
                                  active_connections_, idle_connections_.size());
                     
                     return connection;
@@ -413,6 +423,7 @@ ConnectionPool::PoolStats ConnectionPool::get_statistics() const {
     stats.total_releases = total_releases_;
     stats.wait_count = wait_count_;
     stats.total_wait_time_ms = total_wait_time_;
+    stats.avg_wait_time = wait_count_ > 0 ? std::chrono::milliseconds(total_wait_time_ / wait_count_) : std::chrono::milliseconds(0);
     
     return stats;
 }x