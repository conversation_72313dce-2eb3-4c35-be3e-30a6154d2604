
[0;34m==== Starting Development Environment ====[0m

[0;34m[INFO][0m Rendering configuration for dev environment...
[0;34m[INFO][0m Loading configuration for dev environment...
[0;32m[SUCCESS][0m Configuration validation passed for dev
[0;34m[INFO][0m Rendering configuration template...
[0;32m[SUCCESS][0m Configuration rendered: /Users/<USER>/uclwork/etl/omop-etl/scripts/build.env
[0;32m[SUCCESS][0m Build configuration generated for dev environment
[0;34m[INFO][0m Configuration file: /Users/<USER>/uclwork/etl/omop-etl/scripts/build.env
[0;34m[INFO][0m You can now run: ./scripts/build.sh build
[0;32m[SUCCESS][0m Configuration rendered successfully
[0;34m[INFO][0m Configuration loaded from build.env
[0;34m[INFO][0m Starting development services...
Compose now can delegate build to bake for better performances
Just set COMPOSE_BAKE=true
#0 building with "multiarch-builder" instance using docker-container driver

#1 [omop-etl-api internal] load build definition from Dockerfile
#1 transferring dockerfile: 16.28kB done
#1 DONE 0.0s

#2 [omop-etl-dev internal] load build definition from Dockerfile
#2 transferring dockerfile: 16.28kB 0.0s done
#2 DONE 0.1s

#3 [omop-etl-dev internal] load metadata for docker.io/library/ubuntu:22.04
#3 DONE 0.5s

#4 [omop-etl-api internal] load .dockerignore
#4 transferring context: 1.20kB done
#4 DONE 0.0s

#5 [omop-etl-dev internal] load .dockerignore
#5 transferring context: 1.20kB 0.0s done
#5 DONE 0.0s

#6 [omop-etl-api runtime  2/10] RUN apt-get update && apt-get install -y     libpq5     unixodbc     libyaml-cpp0.7     libspdlog1     libfmt8     libssl3     uuid-runtime     ca-certificates     curl     libcurl4     && rm -rf /var/lib/apt/lists/*
#6 CACHED

#7 [omop-etl-api runtime  3/10] RUN useradd -m -s /bin/bash omop &&     mkdir -p /etc/omop-etl /var/log/omop-etl /var/lib/omop-etl/data &&     chown -R omop:omop /etc/omop-etl /var/log/omop-etl /var/lib/omop-etl
#7 CACHED

#8 [omop-etl-api base  1/13] FROM docker.io/library/ubuntu:22.04@sha256:1ec65b2719518e27d4d25f104d93f9fac60dc437f81452302406825c46fcc9cb
#8 resolve docker.io/library/ubuntu:22.04@sha256:1ec65b2719518e27d4d25f104d93f9fac60dc437f81452302406825c46fcc9cb done
#8 DONE 0.0s

#9 [omop-etl-api base  3/13] RUN apt-get install -y     gcc-13     g++-13     build-essential     git     pkg-config     ninja-build     libpq-dev     unixodbc-dev     libyaml-cpp-dev     nlohmann-json3-dev     libspdlog-dev     libfmt-dev     libssl-dev     uuid-dev     zlib1g-dev     wget     curl     libcurl4-openssl-dev     unzip     ca-certificates     && rm -rf /var/lib/apt/lists/*
#9 CACHED

#10 [omop-etl-api base  2/13] RUN apt-get update && apt-get install -y software-properties-common &&     add-apt-repository ppa:ubuntu-toolchain-r/test &&     apt-get update
#10 CACHED

#11 [omop-etl-api base  4/13] RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 100 &&     update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 100
#11 CACHED

#12 [omop-etl-api internal] load build context
#12 transferring context: 303.79kB 0.2s done
#12 DONE 0.2s

#13 [omop-etl-api base  7/13] RUN if [ "all" = "api" ] || [ "all" = "service" ] || [ "all" = "extract" ] || [ "all" = "load" ]; then         apt-get update && apt-get install -y         libmysqlclient-dev         && rm -rf /var/lib/apt/lists/*;     fi
#13 CACHED

#14 [omop-etl-api base  8/13] RUN if [ "True" = "true" ]; then         apt-get update && apt-get install -y         libgrpc++-dev         libprotobuf-dev         protobuf-compiler-grpc         && rm -rf /var/lib/apt/lists/*;     fi
#14 CACHED

#15 [omop-etl-api builder 2/6] COPY . .
#15 CACHED

#16 [omop-etl-api base  6/13] RUN if [ "True" = "true" ]; then         apt-get update && apt-get install -y         libgtest-dev         libgmock-dev         && rm -rf /var/lib/apt/lists/*;     fi
#16 CACHED

#17 [omop-etl-api builder 1/6] WORKDIR /build
#17 CACHED

#18 [omop-etl-api base 13/13] RUN if [ "false" = "true" ]; then         useradd -m -s /bin/bash dev &&         usermod -aG sudo dev &&         echo "dev ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers;     fi
#18 CACHED

#19 [omop-etl-api base 12/13] RUN wget https://github.com/yhirose/cpp-httplib/archive/refs/tags/v0.14.0.tar.gz &&     tar -xzf v0.14.0.tar.gz &&     cp cpp-httplib-0.14.0/httplib.h /usr/local/include/ &&     rm -rf cpp-httplib-0.14.0 v0.14.0.tar.gz
#19 CACHED

#20 [omop-etl-api base 11/13] RUN echo "Target platform: unknown" &&     echo "Target architecture: unknown" &&     ARCH=$(uname -m) &&     echo "Detected runtime architecture: $ARCH" &&     case "$ARCH" in         "amd64"|"x86_64") CMAKE_ARCH="x86_64" ;;         "arm64"|"aarch64") CMAKE_ARCH="aarch64" ;;         "arm/v7"|"armhf") CMAKE_ARCH="armhf" ;;         *)             echo "Unsupported architecture: $ARCH";             echo "Falling back to system architecture detection";             case "$ARCH" in                 "x86_64") CMAKE_ARCH="x86_64" ;;                 "aarch64") CMAKE_ARCH="aarch64" ;;                 "armv7l") CMAKE_ARCH="armhf" ;;                 *) echo "ERROR: Unsupported architecture $ARCH" && exit 1 ;;             esac ;;     esac &&     echo "Using CMake architecture: $CMAKE_ARCH" &&     CMAKE_VERSION="3.28.1" &&     CMAKE_URL="https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" &&     echo "Downloading CMake from: $CMAKE_URL" &&     wget "$CMAKE_URL" &&     tar -xzf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}.tar.gz" &&     cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/bin/"* /usr/local/bin/ &&     cp -r "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}/share/"* /usr/local/share/ &&     rm -rf "cmake-${CMAKE_VERSION}-linux-${CMAKE_ARCH}"* &&     cmake --version
#20 CACHED

#21 [omop-etl-api base  9/13] RUN if [ "True" = "true" ]; then         apt-get update && apt-get install -y         libcpprest-dev         && rm -rf /var/lib/apt/lists/*;     fi
#21 CACHED

#22 [omop-etl-api base 10/13] RUN if [ "all" = "load" ] || [ "all" = "extract" ]; then         apt-get update && apt-get install -y         libarchive-dev         && rm -rf /var/lib/apt/lists/*;     fi
#22 CACHED

#23 [omop-etl-api base  5/13] RUN if [ "false" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#23 CACHED

#24 [omop-etl-api builder 3/6] RUN CMAKE_BUILD_TYPE=$(echo "debug" | tr '[:lower:]' '[:upper:]') &&     CMAKE_ARGS="-DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE -GNinja" &&     if [ "True" = "true" ]; then         CMAKE_ARGS="$CMAKE_ARGS -DBUILD_TESTING=ON";     else         CMAKE_ARGS="$CMAKE_ARGS -DBUILD_TESTING=OFF";     fi &&     if [ "True" = "true" ]; then         CMAKE_ARGS="$CMAKE_ARGS -DENABLE_GRPC=ON";     fi &&     if [ "True" = "true" ]; then         CMAKE_ARGS="$CMAKE_ARGS -DENABLE_REST_API=ON";     fi &&     CMAKE_ARGS="$CMAKE_ARGS -DYAML_CPP_BUILD_TESTS=OFF" &&     echo "CMake configuration: cmake -B build -S . $CMAKE_ARGS" &&     cmake -B build -S . $CMAKE_ARGS
#24 CACHED

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 0.064 Building target: all
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 0.445 Get:1 http://ports.ubuntu.com/ubuntu-ports jammy InRelease [270 kB]
#26 0.546 Get:2 https://ppa.launchpadcontent.net/ubuntu-toolchain-r/test/ubuntu jammy InRelease [24.6 kB]
#26 0.779 Get:3 https://ppa.launchpadcontent.net/ubuntu-toolchain-r/test/ubuntu jammy/main arm64 Packages [9576 B]
#26 0.900 Get:4 http://ports.ubuntu.com/ubuntu-ports jammy-updates InRelease [128 kB]
#26 1.022 Get:5 http://ports.ubuntu.com/ubuntu-ports jammy-backports InRelease [127 kB]
#26 1.137 Get:6 http://ports.ubuntu.com/ubuntu-ports jammy-security InRelease [129 kB]
#26 1.256 Get:7 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 Packages [17.2 MB]
#26 3.644 Get:8 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 Packages [1758 kB]
#26 3.763 Get:9 http://ports.ubuntu.com/ubuntu-ports jammy/multiverse arm64 Packages [224 kB]
#26 3.768 Get:10 http://ports.ubuntu.com/ubuntu-ports jammy/restricted arm64 Packages [24.2 kB]
#26 3.768 Get:11 http://ports.ubuntu.com/ubuntu-ports jammy-updates/multiverse arm64 Packages [52.7 kB]
#26 3.768 Get:12 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 Packages [1567 kB]
#26 4.020 Get:13 http://ports.ubuntu.com/ubuntu-ports jammy-updates/restricted arm64 Packages [4545 kB]
#26 4.623 Get:14 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 Packages [3169 kB]
#26 5.043 Get:15 http://ports.ubuntu.com/ubuntu-ports jammy-backports/main arm64 Packages [82.8 kB]
#26 5.043 Get:16 http://ports.ubuntu.com/ubuntu-ports jammy-backports/universe arm64 Packages [33.3 kB]
#26 5.043 Get:17 http://ports.ubuntu.com/ubuntu-ports jammy-security/multiverse arm64 Packages [27.2 kB]
#26 5.056 Get:18 http://ports.ubuntu.com/ubuntu-ports jammy-security/main arm64 Packages [2865 kB]
#26 5.413 Get:19 http://ports.ubuntu.com/ubuntu-ports jammy-security/universe arm64 Packages [1268 kB]
#26 5.586 Get:20 http://ports.ubuntu.com/ubuntu-ports jammy-security/restricted arm64 Packages [4358 kB]
#26 6.779 Fetched 37.9 MB in 6s (6084 kB/s)
#26 6.779 Reading package lists...
#26 10.89 Reading package lists...
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 13.11 [1/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 10.89 Reading package lists...
#26 14.42 Building dependency tree...
#26 14.98 Reading state information...
#26 15.69 python3 is already the newest version (3.10.6-1~22.04.1).
#26 15.69 python3 set to manually installed.
#26 15.69 The following additional packages will be installed:
#26 15.69   binfmt-support clang-14 clang-format-14 clang-tidy-14 clang-tools-14
#26 15.69   icu-devtools javascript-common libbabeltrace1 libboost-regex1.74.0 libc6-dbg
#26 15.69   libclang-common-14-dev libclang-cpp14 libclang1-14 libdebuginfod-common
#26 15.69   libdebuginfod1 libexpat1-dev libffi-dev libgc1 libgpm2 libicu-dev libjq1
#26 15.69   libjs-jquery libjs-sphinxdoc libjs-underscore libllvm14 libncurses-dev
#26 15.69   libnl-3-200 libnl-genl-3-200 libobjc-11-dev libobjc4 libonig5 libpfm4
#26 15.69   libpipeline1 libpython3-dev libpython3.10 libpython3.10-dev libsodium23
#26 15.69   libsource-highlight-common libsource-highlight4v5 libtinfo-dev libtinyxml2-9
#26 15.69   libxml2-dev libz3-4 libz3-dev llvm-14 llvm-14-dev llvm-14-linker-tools
#26 15.69   llvm-14-runtime llvm-14-tools python3-dev python3-distutils python3-lib2to3
#26 15.69   python3-pygments python3-setuptools python3-wheel python3-yaml
#26 15.70   python3.10-dev vim-common vim-runtime xxd
#26 15.70 Suggested packages:
#26 15.70   clang-14-doc cppcheck-gui gdb-doc gdbserver lm-sensors lsof strace apache2
#26 15.70   | lighttpd | httpd gpm icu-doc ncurses-doc llvm-14-doc hunspell
#26 15.70   python-pygments-doc ttf-bitstream-vera python-setuptools-doc valgrind-dbg
#26 15.70   valgrind-mpi kcachegrind alleyoop valkyrie ctags vim-doc vim-scripts
#26 17.09 The following NEW packages will be installed:
#26 17.10   binfmt-support clang clang-14 clang-format clang-format-14 clang-tidy
#26 17.10   clang-tidy-14 clang-tools clang-tools-14 cppcheck gdb htop icu-devtools
#26 17.10   javascript-common jq libbabeltrace1 libboost-regex1.74.0 libc6-dbg
#26 17.10   libclang-common-14-dev libclang-cpp14 libclang1-14 libdebuginfod-common
#26 17.10   libdebuginfod1 libexpat1-dev libffi-dev libgc1 libgpm2 libicu-dev libjq1
#26 17.10   libjs-jquery libjs-sphinxdoc libjs-underscore libllvm14 libncurses-dev
#26 17.10   libnl-3-200 libnl-genl-3-200 libobjc-11-dev libobjc4 libonig5 libpfm4
#26 17.10   libpipeline1 libpython3-dev libpython3.10 libpython3.10-dev libsodium23
#26 17.10   libsource-highlight-common libsource-highlight4v5 libtinfo-dev libtinyxml2-9
#26 17.10   libxml2-dev libz3-4 libz3-dev llvm-14 llvm-14-dev llvm-14-linker-tools
#26 17.10   llvm-14-runtime llvm-14-tools nano python3-dev python3-distutils
#26 17.10   python3-lib2to3 python3-pip python3-pygments python3-setuptools
#26 17.10   python3-wheel python3-yaml python3.10-dev tree valgrind vim vim-common
#26 17.10   vim-runtime xxd
#26 17.44 0 upgraded, 73 newly installed, 0 to remove and 0 not upgraded.
#26 17.44 Need to get 169 MB of archives.
#26 17.44 After this operation, 846 MB of additional disk space will be used.
#26 17.44 Get:1 https://ppa.launchpadcontent.net/ubuntu-toolchain-r/test/ubuntu jammy/main arm64 libobjc4 arm64 13.1.0-8ubuntu1~22.04 [46.1 kB]
#26 17.44 Get:2 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libdebuginfod-common all 0.186-1ubuntu0.1 [7996 B]
#26 17.56 Get:3 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 python3-yaml arm64 5.4.1-1ubuntu1 [122 kB]
#26 17.78 Get:4 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 xxd arm64 2:8.2.3995-1ubuntu2.24 [50.9 kB]
#26 17.82 Get:5 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 vim-common all 2:8.2.3995-1ubuntu2.24 [81.5 kB]
#26 17.86 Get:6 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libpipeline1 arm64 1.5.5-1 [23.2 kB]
#26 17.88 Get:7 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 nano arm64 6.2-1ubuntu0.1 [277 kB]
#26 17.99 Get:8 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 binfmt-support arm64 2.2.1-2 [55.0 kB]
#26 17.99 Get:9 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libllvm14 arm64 1:14.0.0-1ubuntu1.1 [22.6 MB]
#26 21.07 Get:10 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 libclang-cpp14 arm64 1:14.0.0-1ubuntu1.1 [11.5 MB]
#26 22.49 Get:11 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libgc1 arm64 1:8.0.6-1.1build1 [94.2 kB]
#26 22.50 Get:12 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 libobjc-11-dev arm64 11.4.0-1ubuntu1~22.04 [192 kB]
#26 22.52 Get:13 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 libclang-common-14-dev arm64 1:14.0.0-1ubuntu1.1 [4488 kB]
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 22.76 [2/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 23.07 Get:14 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 llvm-14-linker-tools arm64 1:14.0.0-1ubuntu1.1 [1237 kB]
#26 23.36 Get:15 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 libclang1-14 arm64 1:14.0.0-1ubuntu1.1 [6470 kB]
#26 24.23 Get:16 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 clang-14 arm64 1:14.0.0-1ubuntu1.1 [77.5 kB]
#26 24.23 Get:17 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 clang arm64 1:14.0-55~exp2 [3558 B]
#26 24.23 Get:18 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 clang-format-14 arm64 1:14.0.0-1ubuntu1.1 [49.9 kB]
#26 24.23 Get:19 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 clang-format arm64 1:14.0-55~exp2 [3694 B]
#26 24.23 Get:20 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 clang-tools-14 arm64 1:14.0.0-1ubuntu1.1 [6585 kB]
#26 25.01 Get:21 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 clang-tidy-14 arm64 1:14.0.0-1ubuntu1.1 [1693 kB]
#26 25.13 Get:22 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 clang-tidy arm64 1:14.0-55~exp2 [3456 B]
#26 25.15 Get:23 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 clang-tools arm64 1:14.0-55~exp2 [3500 B]
#26 25.15 Get:24 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 libtinyxml2-9 arm64 9.0.0+dfsg-3 [31.9 kB]
#26 25.15 Get:25 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 libz3-4 arm64 4.8.12-1 [5352 kB]
#26 25.99 Get:26 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3-pygments all 2.11.2+dfsg-2ubuntu0.1 [750 kB]
#26 26.04 Get:27 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 cppcheck arm64 2.7-1 [1916 kB]
#26 26.27 Get:28 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libbabeltrace1 arm64 1.5.8-2build1 [155 kB]
#26 26.30 Get:29 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libdebuginfod1 arm64 0.186-1ubuntu0.1 [12.3 kB]
#26 26.31 Get:30 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libpython3.10 arm64 3.10.12-1~22.04.10 [1885 kB]
#26 26.42 Get:31 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libsource-highlight-common all 3.1.9-4.1build2 [64.5 kB]
#26 26.42 Get:32 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libboost-regex1.74.0 arm64 1.74.0-14ubuntu3 [488 kB]
#26 26.67 Get:33 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libsource-highlight4v5 arm64 3.1.9-4.1build2 [195 kB]
#26 26.67 Get:34 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 gdb arm64 12.1-0ubuntu1~22.04.2 [4507 kB]
#26 27.15 Get:35 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libnl-3-200 arm64 3.5.0-0.1 [58.1 kB]
#26 27.15 Get:36 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libnl-genl-3-200 arm64 3.5.0-0.1 [12.1 kB]
#26 27.15 Get:37 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 htop arm64 3.0.5-7build2 [127 kB]
#26 27.15 Get:38 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 icu-devtools arm64 70.1-2 [193 kB]
#26 27.25 Get:39 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 javascript-common all 11+nmu1 [5936 B]
#26 27.25 Get:40 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libonig5 arm64 *******-2build1 [169 kB]
#26 27.27 Get:41 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libjq1 arm64 1.6-2.1ubuntu3.1 [126 kB]
#26 27.29 Get:42 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 jq arm64 1.6-2.1ubuntu3.1 [52.1 kB]
#26 27.29 Get:43 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libexpat1-dev arm64 2.4.7-1ubuntu0.6 [130 kB]
#26 27.33 Get:44 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libgpm2 arm64 1.20.7-10build1 [15.5 kB]
#26 27.33 Get:45 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libicu-dev arm64 70.1-2 [11.6 MB]
#26 29.27 Get:46 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libjs-jquery all 3.6.0+dfsg+~3.5.13-1 [321 kB]
#26 29.28 Get:47 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libjs-underscore all 1.13.2~dfsg-2 [118 kB]
#26 29.28 Get:48 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libjs-sphinxdoc all 4.3.2-1 [139 kB]
#26 29.30 Get:49 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libncurses-dev arm64 6.3-2ubuntu0.1 [381 kB]
#26 29.34 Get:50 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libpython3.10-dev arm64 3.10.12-1~22.04.10 [4666 kB]
#26 29.90 Get:51 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libpython3-dev arm64 3.10.6-1~22.04.1 [7064 B]
#26 29.90 Get:52 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libsodium23 arm64 1.0.18-1build2 [123 kB]
#26 30.23 Get:53 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libtinfo-dev arm64 6.3-2ubuntu0.1 [780 B]
#26 30.23 Get:54 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libxml2-dev arm64 2.9.13+dfsg-1ubuntu0.7 [804 kB]
#26 30.23 Get:55 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 llvm-14-runtime arm64 1:14.0.0-1ubuntu1.1 [456 kB]
#26 30.26 Get:56 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libpfm4 arm64 4.11.1+git32-gd0b85fb-1ubuntu0.1 [50.4 kB]
#26 30.27 Get:57 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 llvm-14 arm64 1:14.0.0-1ubuntu1.1 [11.5 MB]
#26 31.83 Get:58 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 libffi-dev arm64 3.4.2-4 [61.0 kB]
#26 31.83 Get:59 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 llvm-14-tools arm64 1:14.0.0-1ubuntu1.1 [395 kB]
#26 31.85 Get:60 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 libz3-dev arm64 4.8.12-1 [72.2 kB]
#26 31.85 Get:61 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 llvm-14-dev arm64 1:14.0.0-1ubuntu1.1 [36.3 MB]
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 35.23 [3/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/metrics_collector.cpp.o
#25 38.76 [4/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 36.33 Get:62 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3.10-dev arm64 3.10.12-1~22.04.10 [508 kB]
#26 36.34 Get:63 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3-lib2to3 all 3.10.8-1~22.04 [77.6 kB]
#26 36.36 Get:64 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3-distutils all 3.10.8-1~22.04 [139 kB]
#26 36.54 Get:65 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3-dev arm64 3.10.6-1~22.04.1 [26.0 kB]
#26 36.55 Get:66 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 python3-setuptools all 59.6.0-1.2ubuntu0.22.04.3 [340 kB]
#26 36.56 Get:67 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 python3-wheel all 0.37.1-2ubuntu0.22.04.1 [32.0 kB]
#26 36.57 Get:68 http://ports.ubuntu.com/ubuntu-ports jammy-updates/universe arm64 python3-pip all 22.0.2+dfsg-1ubuntu0.6 [1306 kB]
#26 36.73 Get:69 http://ports.ubuntu.com/ubuntu-ports jammy/universe arm64 tree arm64 2.0.2-1 [47.2 kB]
#26 36.73 Get:70 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 libc6-dbg arm64 2.35-0ubuntu3.10 [11.2 MB]
#26 38.19 Get:71 http://ports.ubuntu.com/ubuntu-ports jammy/main arm64 valgrind arm64 1:3.18.1-1ubuntu2 [8169 kB]
#26 39.12 Get:72 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 vim-runtime all 2:8.2.3995-1ubuntu2.24 [6833 kB]
#26 40.08 Get:73 http://ports.ubuntu.com/ubuntu-ports jammy-updates/main arm64 vim arm64 2:8.2.3995-1ubuntu2.24 [1664 kB]
#26 41.21 debconf: delaying package configuration, since apt-utils is not installed
#26 41.59 Fetched 169 MB in 23s (7348 kB/s)
#26 41.99 Selecting previously unselected package libdebuginfod-common.
#26 42.00 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 21462 files and directories currently installed.)
#26 43.01 Preparing to unpack .../00-libdebuginfod-common_0.186-1ubuntu0.1_all.deb ...
#26 43.01 Unpacking libdebuginfod-common (0.186-1ubuntu0.1) ...
#26 43.36 Selecting previously unselected package python3-yaml.
#26 43.36 Preparing to unpack .../01-python3-yaml_5.4.1-1ubuntu1_arm64.deb ...
#26 43.39 Unpacking python3-yaml (5.4.1-1ubuntu1) ...
#26 43.59 Selecting previously unselected package xxd.
#26 43.59 Preparing to unpack .../02-xxd_2%3a8.2.3995-1ubuntu2.24_arm64.deb ...
#26 43.62 Unpacking xxd (2:8.2.3995-1ubuntu2.24) ...
#26 43.89 Selecting previously unselected package vim-common.
#26 43.90 Preparing to unpack .../03-vim-common_2%3a8.2.3995-1ubuntu2.24_all.deb ...
#26 43.98 Unpacking vim-common (2:8.2.3995-1ubuntu2.24) ...
#26 44.31 Selecting previously unselected package libpipeline1:arm64.
#26 44.31 Preparing to unpack .../04-libpipeline1_1.5.5-1_arm64.deb ...
#26 44.35 Unpacking libpipeline1:arm64 (1.5.5-1) ...
#26 44.57 Selecting previously unselected package nano.
#26 44.58 Preparing to unpack .../05-nano_6.2-1ubuntu0.1_arm64.deb ...
#26 44.61 Unpacking nano (6.2-1ubuntu0.1) ...
#26 45.02 Selecting previously unselected package binfmt-support.
#26 45.02 Preparing to unpack .../06-binfmt-support_2.2.1-2_arm64.deb ...
#26 45.04 Unpacking binfmt-support (2.2.1-2) ...
#26 45.32 Selecting previously unselected package libllvm14:arm64.
#26 45.34 Preparing to unpack .../07-libllvm14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 45.48 Unpacking libllvm14:arm64 (1:14.0.0-1ubuntu1.1) ...
#26 47.94 Selecting previously unselected package libclang-cpp14.
#26 47.96 Preparing to unpack .../08-libclang-cpp14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 48.02 Unpacking libclang-cpp14 (1:14.0.0-1ubuntu1.1) ...
#26 49.23 Selecting previously unselected package libgc1:arm64.
#26 49.24 Preparing to unpack .../09-libgc1_1%3a8.0.6-1.1build1_arm64.deb ...
#26 49.26 Unpacking libgc1:arm64 (1:8.0.6-1.1build1) ...
#26 49.34 Selecting previously unselected package libobjc4:arm64.
#26 49.34 Preparing to unpack .../10-libobjc4_13.1.0-8ubuntu1~22.04_arm64.deb ...
#26 49.35 Unpacking libobjc4:arm64 (13.1.0-8ubuntu1~22.04) ...
#26 49.45 Selecting previously unselected package libobjc-11-dev:arm64.
#26 49.45 Preparing to unpack .../11-libobjc-11-dev_11.4.0-1ubuntu1~22.04_arm64.deb ...
#26 49.47 Unpacking libobjc-11-dev:arm64 (11.4.0-1ubuntu1~22.04) ...
#26 49.58 Selecting previously unselected package libclang-common-14-dev.
#26 49.58 Preparing to unpack .../12-libclang-common-14-dev_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 49.59 Unpacking libclang-common-14-dev (1:14.0.0-1ubuntu1.1) ...
#26 50.96 Selecting previously unselected package llvm-14-linker-tools.
#26 50.97 Preparing to unpack .../13-llvm-14-linker-tools_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 50.98 Unpacking llvm-14-linker-tools (1:14.0.0-1ubuntu1.1) ...
#26 51.11 Selecting previously unselected package libclang1-14.
#26 51.11 Preparing to unpack .../14-libclang1-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 51.11 Unpacking libclang1-14 (1:14.0.0-1ubuntu1.1) ...
#26 51.61 Selecting previously unselected package clang-14.
#26 51.61 Preparing to unpack .../15-clang-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 51.62 Unpacking clang-14 (1:14.0.0-1ubuntu1.1) ...
#26 51.71 Selecting previously unselected package clang.
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 41.40 [5/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/http_client.cpp.o
#25 46.44 [6/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/encoding.cpp.o
#25 46.45 FAILED: src/lib/core/CMakeFiles/omop_core.dir/encoding.cpp.o 
#25 46.46 /usr/bin/c++ -DFMT_LOCALE -DFMT_SHARED -DJSON_DIAGNOSTICS=0 -DJSON_USE_IMPLICIT_CONVERSIONS=1 -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSPDLOG_SHARED_LIB -I/usr/include/postgresql -I/build/build/include -I/build/src/lib -I/build/src/lib/core/.. -I/build/src/lib/core -I/build/src/lib/common/.. -g -std=c++20 -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -Wno-unused-parameter -O3 -MD -MT src/lib/core/CMakeFiles/omop_core.dir/encoding.cpp.o -MF src/lib/core/CMakeFiles/omop_core.dir/encoding.cpp.o.d -o src/lib/core/CMakeFiles/omop_core.dir/encoding.cpp.o -c /build/src/lib/core/encoding.cpp
#25 46.46 /build/src/lib/core/encoding.cpp:397:48: warning: multi-character character constant [-Wmultichar]
#25 46.46   397 |     std::replace(result.begin(), result.end(), '\u00A0', ' '); // Non-breaking space
#25 46.46       |                                                ^~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:398:48: warning: multi-character character constant [-Wmultichar]
#25 46.46   398 |     std::replace(result.begin(), result.end(), '\u2018', '\''); // Left single quote
#25 46.46       |                                                ^~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:399:48: warning: multi-character character constant [-Wmultichar]
#25 46.46   399 |     std::replace(result.begin(), result.end(), '\u2019', '\''); // Right single quote
#25 46.46       |                                                ^~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:400:48: warning: multi-character character constant [-Wmultichar]
#25 46.46   400 |     std::replace(result.begin(), result.end(), '\u201C', '"'); // Left double quote
#25 46.46       |                                                ^~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:401:48: warning: multi-character character constant [-Wmultichar]
#25 46.46   401 |     std::replace(result.begin(), result.end(), '\u201D', '"'); // Right double quote
#25 46.46       |                                                ^~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:21:1: error: definition of explicitly-defaulted 'omop::core::TextEncoder::TextEncoder()'
#25 46.46    21 | TextEncoder::TextEncoder() = default;
#25 46.46       | ^~~~~~~~~~~
#25 46.46 In file included from /build/src/lib/core/encoding.cpp:8:
#25 46.46 /build/src/lib/core/encoding.h:247:5: note: 'omop::core::TextEncoder::TextEncoder()' explicitly defaulted here
#25 46.46   247 |     TextEncoder() = default;
#25 46.46       |     ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:23:1: error: definition of explicitly-defaulted 'omop::core::TextEncoder::~TextEncoder()'
#25 46.46    23 | TextEncoder::~TextEncoder() = default;
#25 46.46       | ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.h:248:5: note: 'virtual omop::core::TextEncoder::~TextEncoder()' explicitly defaulted here
#25 46.46   248 |     ~TextEncoder() override = default;
#25 46.46       |     ^
#25 46.46 /build/src/lib/core/encoding.cpp: In member function 'std::string omop::core::TextEncoder::normalize_compatibility(const std::string&)':
#25 46.46 /build/src/lib/core/encoding.cpp:397:17: error: no matching function for call to 'replace(std::__cxx11::basic_string<char>::iterator, std::__cxx11::basic_string<char>::iterator, int, char)'
#25 46.46   397 |     std::replace(result.begin(), result.end(), '\u00A0', ' '); // Non-breaking space
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 In file included from /usr/include/c++/13/chrono:48,
#25 46.46                  from /build/src/lib/core/encoding.h:9:
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note: candidate: 'template<class _FIter, class _Tp> constexpr void std::replace(_FIter, _FIter, const _Tp&, const _Tp&)'
#25 46.46  4369 |     replace(_ForwardIterator __first, _ForwardIterator __last,
#25 46.46       |     ^~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:397:17: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'char')
#25 46.46   397 |     std::replace(result.begin(), result.end(), '\u00A0', ' '); // Non-breaking space
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 In file included from /usr/include/c++/13/algorithm:73,
#25 46.46                  from /build/src/lib/core/encoding.cpp:13:
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note: candidate: 'template<class _ExecutionPolicy, class _ForwardIterator, class _Tp> __pstl::__internal::__enable_if_execution_policy<_ExecutionPolicy, void> std::replace(_ExecutionPolicy&&, _ForwardIterator, _ForwardIterator, const _Tp&, const _Tp&)'
#25 46.46   174 | replace(_ExecutionPolicy&& __exec, _ForwardIterator __first, _ForwardIterator __last, const _Tp& __old_value,
#25 46.46       | ^~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:397:17: note:   deduced conflicting types for parameter '_ForwardIterator' ('__gnu_cxx::__normal_iterator<char*, std::__cxx11::basic_string<char> >' and 'int')
#25 46.46   397 |     std::replace(result.begin(), result.end(), '\u00A0', ' '); // Non-breaking space
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:398:17: error: no matching function for call to 'replace(std::__cxx11::basic_string<char>::iterator, std::__cxx11::basic_string<char>::iterator, int, char)'
#25 46.46   398 |     std::replace(result.begin(), result.end(), '\u2018', '\''); // Left single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note: candidate: 'template<class _FIter, class _Tp> constexpr void std::replace(_FIter, _FIter, const _Tp&, const _Tp&)'
#25 46.46  4369 |     replace(_ForwardIterator __first, _ForwardIterator __last,
#25 46.46       |     ^~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:398:17: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'char')
#25 46.46   398 |     std::replace(result.begin(), result.end(), '\u2018', '\''); // Left single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note: candidate: 'template<class _ExecutionPolicy, class _ForwardIterator, class _Tp> __pstl::__internal::__enable_if_execution_policy<_ExecutionPolicy, void> std::replace(_ExecutionPolicy&&, _ForwardIterator, _ForwardIterator, const _Tp&, const _Tp&)'
#25 46.46   174 | replace(_ExecutionPolicy&& __exec, _ForwardIterator __first, _ForwardIterator __last, const _Tp& __old_value,
#25 46.46       | ^~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:398:17: note:   deduced conflicting types for parameter '_ForwardIterator' ('__gnu_cxx::__normal_iterator<char*, std::__cxx11::basic_string<char> >' and 'int')
#25 46.46   398 |     std::replace(result.begin(), result.end(), '\u2018', '\''); // Left single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:399:17: error: no matching function for call to 'replace(std::__cxx11::basic_string<char>::iterator, std::__cxx11::basic_string<char>::iterator, int, char)'
#25 46.46   399 |     std::replace(result.begin(), result.end(), '\u2019', '\''); // Right single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note: candidate: 'template<class _FIter, class _Tp> constexpr void std::replace(_FIter, _FIter, const _Tp&, const _Tp&)'
#25 46.46  4369 |     replace(_ForwardIterator __first, _ForwardIterator __last,
#25 46.46       |     ^~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:399:17: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'char')
#25 46.46   399 |     std::replace(result.begin(), result.end(), '\u2019', '\''); // Right single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note: candidate: 'template<class _ExecutionPolicy, class _ForwardIterator, class _Tp> __pstl::__internal::__enable_if_execution_policy<_ExecutionPolicy, void> std::replace(_ExecutionPolicy&&, _ForwardIterator, _ForwardIterator, const _Tp&, const _Tp&)'
#25 46.46   174 | replace(_ExecutionPolicy&& __exec, _ForwardIterator __first, _ForwardIterator __last, const _Tp& __old_value,
#25 46.46       | ^~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:399:17: note:   deduced conflicting types for parameter '_ForwardIterator' ('__gnu_cxx::__normal_iterator<char*, std::__cxx11::basic_string<char> >' and 'int')
#25 46.46   399 |     std::replace(result.begin(), result.end(), '\u2019', '\''); // Right single quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:400:17: error: no matching function for call to 'replace(std::__cxx11::basic_string<char>::iterator, std::__cxx11::basic_string<char>::iterator, int, char)'
#25 46.46   400 |     std::replace(result.begin(), result.end(), '\u201C', '"'); // Left double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note: candidate: 'template<class _FIter, class _Tp> constexpr void std::replace(_FIter, _FIter, const _Tp&, const _Tp&)'
#25 46.46  4369 |     replace(_ForwardIterator __first, _ForwardIterator __last,
#25 46.46       |     ^~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:400:17: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'char')
#25 46.46   400 |     std::replace(result.begin(), result.end(), '\u201C', '"'); // Left double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note: candidate: 'template<class _ExecutionPolicy, class _ForwardIterator, class _Tp> __pstl::__internal::__enable_if_execution_policy<_ExecutionPolicy, void> std::replace(_ExecutionPolicy&&, _ForwardIterator, _ForwardIterator, const _Tp&, const _Tp&)'
#25 46.46   174 | replace(_ExecutionPolicy&& __exec, _ForwardIterator __first, _ForwardIterator __last, const _Tp& __old_value,
#25 46.46       | ^~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:400:17: note:   deduced conflicting types for parameter '_ForwardIterator' ('__gnu_cxx::__normal_iterator<char*, std::__cxx11::basic_string<char> >' and 'int')
#25 46.46   400 |     std::replace(result.begin(), result.end(), '\u201C', '"'); // Left double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:401:17: error: no matching function for call to 'replace(std::__cxx11::basic_string<char>::iterator, std::__cxx11::basic_string<char>::iterator, int, char)'
#25 46.46   401 |     std::replace(result.begin(), result.end(), '\u201D', '"'); // Right double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note: candidate: 'template<class _FIter, class _Tp> constexpr void std::replace(_FIter, _FIter, const _Tp&, const _Tp&)'
#25 46.46  4369 |     replace(_ForwardIterator __first, _ForwardIterator __last,
#25 46.46       |     ^~~~~~~
#25 46.46 /usr/include/c++/13/bits/stl_algo.h:4369:5: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:401:17: note:   deduced conflicting types for parameter 'const _Tp' ('int' and 'char')
#25 46.46   401 |     std::replace(result.begin(), result.end(), '\u201D', '"'); // Right double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note: candidate: 'template<class _ExecutionPolicy, class _ForwardIterator, class _Tp> __pstl::__internal::__enable_if_execution_policy<_ExecutionPolicy, void> std::replace(_ExecutionPolicy&&, _ForwardIterator, _ForwardIterator, const _Tp&, const _Tp&)'
#25 46.46   174 | replace(_ExecutionPolicy&& __exec, _ForwardIterator __first, _ForwardIterator __last, const _Tp& __old_value,
#25 46.46       | ^~~~~~~
#25 46.46 /usr/include/c++/13/pstl/glue_algorithm_defs.h:174:1: note:   template argument deduction/substitution failed:
#25 46.46 /build/src/lib/core/encoding.cpp:401:17: note:   deduced conflicting types for parameter '_ForwardIterator' ('__gnu_cxx::__normal_iterator<char*, std::__cxx11::basic_string<char> >' and 'int')
#25 46.46   401 |     std::replace(result.begin(), result.end(), '\u201D', '"'); // Right double quote
#25 46.46       |     ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp: At global scope:
#25 46.46 /build/src/lib/core/encoding.cpp:466:15: error: redefinition of 'omop::core::EncodingStats omop::core::TextEncoder::get_statistics()'
#25 46.46   466 | EncodingStats TextEncoder::get_statistics() {
#25 46.46       |               ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:210:15: note: 'virtual omop::core::EncodingStats omop::core::TextEncoder::get_statistics()' previously defined here
#25 46.46   210 | EncodingStats TextEncoder::get_statistics() {
#25 46.46       |               ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:470:6: error: redefinition of 'void omop::core::TextEncoder::reset_statistics()'
#25 46.46   470 | void TextEncoder::reset_statistics() {
#25 46.46       |      ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:216:6: note: 'virtual void omop::core::TextEncoder::reset_statistics()' previously defined here
#25 46.46   216 | void TextEncoder::reset_statistics() {
#25 46.46       |      ^~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp: In function 'omop::core::EncodingConfig omop::core::get_default_encoding_config()':
#25 46.46 /build/src/lib/core/encoding.cpp:491:12: error: 'struct omop::core::EncodingConfig' has no member named 'default_encoding'
#25 46.46   491 |     config.default_encoding = Encoding::UTF8;
#25 46.46       |            ^~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:493:12: error: 'struct omop::core::EncodingConfig' has no member named 'detect_encoding'; did you mean 'source_encoding'?
#25 46.46   493 |     config.detect_encoding = true;
#25 46.46       |            ^~~~~~~~~~~~~~~
#25 46.46       |            source_encoding
#25 46.46 /build/src/lib/core/encoding.cpp:494:12: error: 'struct omop::core::EncodingConfig' has no member named 'normalize_text'
#25 46.46   494 |     config.normalize_text = true;
#25 46.46       |            ^~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:495:12: error: 'struct omop::core::EncodingConfig' has no member named 'normalization_form'
#25 46.46   495 |     config.normalization_form = "NFC";
#25 46.46       |            ^~~~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp: In static member function 'static omop::core::EncodingDetectionResult omop::core::CSVEncodingUtils::detect_csv_encoding(const std::string&)':
#25 46.46 /build/src/lib/core/encoding.cpp:510:21: error: 'read_file_bytes' was not declared in this scope
#25 46.46   510 |         auto data = read_file_bytes(file_path);
#25 46.46       |                     ^~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp: At global scope:
#25 46.46 /build/src/lib/core/encoding.cpp:605:6: error: no declaration matches 'bool omop::core::CSVEncodingUtils::convert_csv_encoding(const std::string&, const std::string&, omop::core::Encoding)'
#25 46.46   605 | bool CSVEncodingUtils::convert_csv_encoding(const std::string& input_file,
#25 46.46       |      ^~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.cpp:598:6: note: candidate is: 'static bool omop::core::CSVEncodingUtils::convert_csv_encoding(const std::string&, const std::string&, omop::core::Encoding, omop::core::Encoding)'
#25 46.46   598 | bool CSVEncodingUtils::convert_csv_encoding(const std::string& input_file,
#25 46.46       |      ^~~~~~~~~~~~~~~~
#25 46.46 /build/src/lib/core/encoding.h:322:7: note: 'class omop::core::CSVEncodingUtils' defined here
#25 46.46   322 | class CSVEncodingUtils {
#25 46.46       |       ^~~~~~~~~~~~~~~~
#25 47.95 [7/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o
#25 48.04 [8/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o
#25 49.37 [9/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 51.72 Preparing to unpack .../16-clang_1%3a14.0-55~exp2_arm64.deb ...
#26 51.74 Unpacking clang (1:14.0-55~exp2) ...
#26 51.88 Selecting previously unselected package clang-format-14.
#26 51.88 Preparing to unpack .../17-clang-format-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 51.89 Unpacking clang-format-14 (1:14.0.0-1ubuntu1.1) ...
#26 51.96 Selecting previously unselected package clang-format:arm64.
#26 51.97 Preparing to unpack .../18-clang-format_1%3a14.0-55~exp2_arm64.deb ...
#26 51.98 Unpacking clang-format:arm64 (1:14.0-55~exp2) ...
#26 52.01 Selecting previously unselected package clang-tools-14.
#26 52.02 Preparing to unpack .../19-clang-tools-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 52.02 Unpacking clang-tools-14 (1:14.0.0-1ubuntu1.1) ...
#26 52.69 Selecting previously unselected package clang-tidy-14.
#26 52.70 Preparing to unpack .../20-clang-tidy-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 52.70 Unpacking clang-tidy-14 (1:14.0.0-1ubuntu1.1) ...
#26 52.87 Selecting previously unselected package clang-tidy.
#26 52.87 Preparing to unpack .../21-clang-tidy_1%3a14.0-55~exp2_arm64.deb ...
#26 52.88 Unpacking clang-tidy (1:14.0-55~exp2) ...
#26 52.93 Selecting previously unselected package clang-tools:arm64.
#26 52.94 Preparing to unpack .../22-clang-tools_1%3a14.0-55~exp2_arm64.deb ...
#26 52.95 Unpacking clang-tools:arm64 (1:14.0-55~exp2) ...
#26 53.01 Selecting previously unselected package libtinyxml2-9:arm64.
#26 53.02 Preparing to unpack .../23-libtinyxml2-9_9.0.0+dfsg-3_arm64.deb ...
#26 53.02 Unpacking libtinyxml2-9:arm64 (9.0.0+dfsg-3) ...
#26 53.08 Selecting previously unselected package libz3-4:arm64.
#26 53.08 Preparing to unpack .../24-libz3-4_4.8.12-1_arm64.deb ...
#26 53.08 Unpacking libz3-4:arm64 (4.8.12-1) ...
#26 53.32 Selecting previously unselected package python3-pygments.
#26 53.32 Preparing to unpack .../25-python3-pygments_2.11.2+dfsg-2ubuntu0.1_all.deb ...
#26 53.33 Unpacking python3-pygments (2.11.2+dfsg-2ubuntu0.1) ...
#26 53.49 Selecting previously unselected package cppcheck.
#26 53.50 Preparing to unpack .../26-cppcheck_2.7-1_arm64.deb ...
#26 53.50 Unpacking cppcheck (2.7-1) ...
#26 53.69 Selecting previously unselected package libbabeltrace1:arm64.
#26 53.70 Preparing to unpack .../27-libbabeltrace1_1.5.8-2build1_arm64.deb ...
#26 53.70 Unpacking libbabeltrace1:arm64 (1.5.8-2build1) ...
#26 53.74 Selecting previously unselected package libdebuginfod1:arm64.
#26 53.75 Preparing to unpack .../28-libdebuginfod1_0.186-1ubuntu0.1_arm64.deb ...
#26 53.76 Unpacking libdebuginfod1:arm64 (0.186-1ubuntu0.1) ...
#26 53.82 Selecting previously unselected package libpython3.10:arm64.
#26 53.82 Preparing to unpack .../29-libpython3.10_3.10.12-1~22.04.10_arm64.deb ...
#26 53.83 Unpacking libpython3.10:arm64 (3.10.12-1~22.04.10) ...
#26 53.96 Selecting previously unselected package libsource-highlight-common.
#26 53.96 Preparing to unpack .../30-libsource-highlight-common_3.1.9-4.1build2_all.deb ...
#26 53.97 Unpacking libsource-highlight-common (3.1.9-4.1build2) ...
#26 54.10 Selecting previously unselected package libboost-regex1.74.0:arm64.
#26 54.10 Preparing to unpack .../31-libboost-regex1.74.0_1.74.0-14ubuntu3_arm64.deb ...
#26 54.12 Unpacking libboost-regex1.74.0:arm64 (1.74.0-14ubuntu3) ...
#26 54.21 Selecting previously unselected package libsource-highlight4v5.
#26 54.22 Preparing to unpack .../32-libsource-highlight4v5_3.1.9-4.1build2_arm64.deb ...
#26 54.23 Unpacking libsource-highlight4v5 (3.1.9-4.1build2) ...
#26 54.26 Selecting previously unselected package gdb.
#26 54.27 Preparing to unpack .../33-gdb_12.1-0ubuntu1~22.04.2_arm64.deb ...
#26 54.27 Unpacking gdb (12.1-0ubuntu1~22.04.2) ...
#26 54.53 Selecting previously unselected package libnl-3-200:arm64.
#26 54.53 Preparing to unpack .../34-libnl-3-200_3.5.0-0.1_arm64.deb ...
#26 54.53 Unpacking libnl-3-200:arm64 (3.5.0-0.1) ...
#26 54.57 Selecting previously unselected package libnl-genl-3-200:arm64.
#26 54.57 Preparing to unpack .../35-libnl-genl-3-200_3.5.0-0.1_arm64.deb ...
#26 54.58 Unpacking libnl-genl-3-200:arm64 (3.5.0-0.1) ...
#26 54.63 Selecting previously unselected package htop.
#26 54.63 Preparing to unpack .../36-htop_3.0.5-7build2_arm64.deb ...
#26 54.64 Unpacking htop (3.0.5-7build2) ...
#26 54.67 Selecting previously unselected package icu-devtools.
#26 54.67 Preparing to unpack .../37-icu-devtools_70.1-2_arm64.deb ...
#26 54.68 Unpacking icu-devtools (70.1-2) ...
#26 54.73 Selecting previously unselected package javascript-common.
#26 54.74 Preparing to unpack .../38-javascript-common_11+nmu1_all.deb ...
#26 54.75 Unpacking javascript-common (11+nmu1) ...
#26 54.78 Selecting previously unselected package libonig5:arm64.
#26 54.79 Preparing to unpack .../39-libonig5_*******-2build1_arm64.deb ...
#26 54.79 Unpacking libonig5:arm64 (*******-2build1) ...
#26 54.85 Selecting previously unselected package libjq1:arm64.
#26 54.85 Preparing to unpack .../40-libjq1_1.6-2.1ubuntu3.1_arm64.deb ...
#26 54.86 Unpacking libjq1:arm64 (1.6-2.1ubuntu3.1) ...
#26 54.91 Selecting previously unselected package jq.
#26 54.92 Preparing to unpack .../41-jq_1.6-2.1ubuntu3.1_arm64.deb ...
#26 54.92 Unpacking jq (1.6-2.1ubuntu3.1) ...
#26 54.96 Selecting previously unselected package libexpat1-dev:arm64.
#26 54.96 Preparing to unpack .../42-libexpat1-dev_2.4.7-1ubuntu0.6_arm64.deb ...
#26 54.97 Unpacking libexpat1-dev:arm64 (2.4.7-1ubuntu0.6) ...
#26 55.06 Selecting previously unselected package libgpm2:arm64.
#26 55.06 Preparing to unpack .../43-libgpm2_1.20.7-10build1_arm64.deb ...
#26 55.07 Unpacking libgpm2:arm64 (1.20.7-10build1) ...
#26 55.10 Selecting previously unselected package libicu-dev:arm64.
#26 55.11 Preparing to unpack .../44-libicu-dev_70.1-2_arm64.deb ...
#26 55.11 Unpacking libicu-dev:arm64 (70.1-2) ...
#26 55.67 Selecting previously unselected package libjs-jquery.
#26 55.68 Preparing to unpack .../45-libjs-jquery_3.6.0+dfsg+~3.5.13-1_all.deb ...
#26 55.70 Unpacking libjs-jquery (3.6.0+dfsg+~3.5.13-1) ...
#26 55.73 Selecting previously unselected package libjs-underscore.
#26 55.74 Preparing to unpack .../46-libjs-underscore_1.13.2~dfsg-2_all.deb ...
#26 55.74 Unpacking libjs-underscore (1.13.2~dfsg-2) ...
#26 55.77 Selecting previously unselected package libjs-sphinxdoc.
#26 55.77 Preparing to unpack .../47-libjs-sphinxdoc_4.3.2-1_all.deb ...
#26 55.78 Unpacking libjs-sphinxdoc (4.3.2-1) ...
#26 55.81 Selecting previously unselected package libncurses-dev:arm64.
#26 55.82 Preparing to unpack .../48-libncurses-dev_6.3-2ubuntu0.1_arm64.deb ...
#26 55.83 Unpacking libncurses-dev:arm64 (6.3-2ubuntu0.1) ...
#26 55.91 Selecting previously unselected package libpython3.10-dev:arm64.
#26 55.91 Preparing to unpack .../49-libpython3.10-dev_3.10.12-1~22.04.10_arm64.deb ...
#26 55.92 Unpacking libpython3.10-dev:arm64 (3.10.12-1~22.04.10) ...
#26 56.36 Selecting previously unselected package libpython3-dev:arm64.
#26 56.37 Preparing to unpack .../50-libpython3-dev_3.10.6-1~22.04.1_arm64.deb ...
#26 56.37 Unpacking libpython3-dev:arm64 (3.10.6-1~22.04.1) ...
#26 56.41 Selecting previously unselected package libsodium23:arm64.
#26 56.41 Preparing to unpack .../51-libsodium23_1.0.18-1build2_arm64.deb ...
#26 56.42 Unpacking libsodium23:arm64 (1.0.18-1build2) ...
#26 56.45 Selecting previously unselected package libtinfo-dev:arm64.
#26 56.45 Preparing to unpack .../52-libtinfo-dev_6.3-2ubuntu0.1_arm64.deb ...
#26 56.46 Unpacking libtinfo-dev:arm64 (6.3-2ubuntu0.1) ...
#26 56.48 Selecting previously unselected package libxml2-dev:arm64.
#26 56.48 Preparing to unpack .../53-libxml2-dev_2.9.13+dfsg-1ubuntu0.7_arm64.deb ...
#26 56.49 Unpacking libxml2-dev:arm64 (2.9.13+dfsg-1ubuntu0.7) ...
#26 56.58 Selecting previously unselected package llvm-14-runtime.
#26 56.58 Preparing to unpack .../54-llvm-14-runtime_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 56.59 Unpacking llvm-14-runtime (1:14.0.0-1ubuntu1.1) ...
#26 56.65 Selecting previously unselected package libpfm4:arm64.
#26 56.65 Preparing to unpack .../55-libpfm4_4.11.1+git32-gd0b85fb-1ubuntu0.1_arm64.deb ...
#26 56.65 Unpacking libpfm4:arm64 (4.11.1+git32-gd0b85fb-1ubuntu0.1) ...
#26 56.68 Selecting previously unselected package llvm-14.
#26 56.69 Preparing to unpack .../56-llvm-14_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 56.70 Unpacking llvm-14 (1:14.0.0-1ubuntu1.1) ...
#26 57.24 Selecting previously unselected package libffi-dev:arm64.
#26 57.25 Preparing to unpack .../57-libffi-dev_3.4.2-4_arm64.deb ...
#26 57.25 Unpacking libffi-dev:arm64 (3.4.2-4) ...
#26 57.29 Selecting previously unselected package llvm-14-tools.
#26 57.29 Preparing to unpack .../58-llvm-14-tools_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 57.30 Unpacking llvm-14-tools (1:14.0.0-1ubuntu1.1) ...
#26 57.54 Selecting previously unselected package libz3-dev:arm64.
#26 57.55 Preparing to unpack .../59-libz3-dev_4.8.12-1_arm64.deb ...
#26 57.55 Unpacking libz3-dev:arm64 (4.8.12-1) ...
#26 57.60 Selecting previously unselected package llvm-14-dev.
#26 57.60 Preparing to unpack .../60-llvm-14-dev_1%3a14.0.0-1ubuntu1.1_arm64.deb ...
#26 57.61 Unpacking llvm-14-dev (1:14.0.0-1ubuntu1.1) ...
#26 61.06 Selecting previously unselected package python3.10-dev.
#26 61.07 Preparing to unpack .../61-python3.10-dev_3.10.12-1~22.04.10_arm64.deb ...
#26 61.08 Unpacking python3.10-dev (3.10.12-1~22.04.10) ...
#26 61.14 Selecting previously unselected package python3-lib2to3.
#26 61.15 Preparing to unpack .../62-python3-lib2to3_3.10.8-1~22.04_all.deb ...
#26 61.15 Unpacking python3-lib2to3 (3.10.8-1~22.04) ...
#26 61.25 Selecting previously unselected package python3-distutils.
#26 61.25 Preparing to unpack .../63-python3-distutils_3.10.8-1~22.04_all.deb ...
#26 61.25 Unpacking python3-distutils (3.10.8-1~22.04) ...
#26 61.29 Selecting previously unselected package python3-dev.
#26 61.29 Preparing to unpack .../64-python3-dev_3.10.6-1~22.04.1_arm64.deb ...
#26 61.29 Unpacking python3-dev (3.10.6-1~22.04.1) ...
#26 61.31 Selecting previously unselected package python3-setuptools.
#26 61.32 Preparing to unpack .../65-python3-setuptools_59.6.0-1.2ubuntu0.22.04.3_all.deb ...
#26 61.32 Unpacking python3-setuptools (59.6.0-1.2ubuntu0.22.04.3) ...
#26 61.36 Selecting previously unselected package python3-wheel.
#26 61.36 Preparing to unpack .../66-python3-wheel_0.37.1-2ubuntu0.22.04.1_all.deb ...
#26 61.37 Unpacking python3-wheel (0.37.1-2ubuntu0.22.04.1) ...
#26 61.39 Selecting previously unselected package python3-pip.
#26 61.39 Preparing to unpack .../67-python3-pip_22.0.2+dfsg-1ubuntu0.6_all.deb ...
#26 61.40 Unpacking python3-pip (22.0.2+dfsg-1ubuntu0.6) ...
#26 61.55 Selecting previously unselected package tree.
#26 61.55 Preparing to unpack .../68-tree_2.0.2-1_arm64.deb ...
#26 61.56 Unpacking tree (2.0.2-1) ...
#26 61.58 Selecting previously unselected package libc6-dbg:arm64.
#26 61.58 Preparing to unpack .../69-libc6-dbg_2.35-0ubuntu3.10_arm64.deb ...
#26 61.58 Unpacking libc6-dbg:arm64 (2.35-0ubuntu3.10) ...
#26 61.77 Selecting previously unselected package valgrind.
#26 61.78 Preparing to unpack .../70-valgrind_1%3a3.18.1-1ubuntu2_arm64.deb ...
#26 61.79 Unpacking valgrind (1:3.18.1-1ubuntu2) ...
#26 62.09 Selecting previously unselected package vim-runtime.
#26 62.10 Preparing to unpack .../71-vim-runtime_2%3a8.2.3995-1ubuntu2.24_all.deb ...
#26 62.12 Adding 'diversion of /usr/share/vim/vim82/doc/help.txt to /usr/share/vim/vim82/doc/help.txt.vim-tiny by vim-runtime'
#26 62.13 Adding 'diversion of /usr/share/vim/vim82/doc/tags to /usr/share/vim/vim82/doc/tags.vim-tiny by vim-runtime'
#26 62.13 Unpacking vim-runtime (2:8.2.3995-1ubuntu2.24) ...
#26 62.57 Selecting previously unselected package vim.
#26 62.58 Preparing to unpack .../72-vim_2%3a8.2.3995-1ubuntu2.24_arm64.deb ...
#26 62.58 Unpacking vim (2:8.2.3995-1ubuntu2.24) ...
#26 62.66 Setting up libpipeline1:arm64 (1.5.5-1) ...
#26 62.67 Setting up javascript-common (11+nmu1) ...
#26 62.69 Setting up libpython3.10:arm64 (3.10.12-1~22.04.10) ...
#26 62.69 Setting up libncurses-dev:arm64 (6.3-2ubuntu0.1) ...
#26 62.70 Setting up libsodium23:arm64 (1.0.18-1build2) ...
#26 62.71 Setting up libgpm2:arm64 (1.20.7-10build1) ...
#26 62.71 Setting up libdebuginfod-common (0.186-1ubuntu0.1) ...
#26 62.81 
#26 62.81 Creating config file /etc/profile.d/debuginfod.sh with new version
#26 62.87 
#26 62.87 Creating config file /etc/profile.d/debuginfod.csh with new version
#26 62.90 Setting up libdebuginfod1:arm64 (0.186-1ubuntu0.1) ...
#26 62.91 Setting up python3-yaml (5.4.1-1ubuntu1) ...
#26 63.05 Setting up libffi-dev:arm64 (3.4.2-4) ...
#26 63.05 Setting up xxd (2:8.2.3995-1ubuntu2.24) ...
#26 63.06 Setting up libsource-highlight-common (3.1.9-4.1build2) ...
#26 63.06 Setting up libc6-dbg:arm64 (2.35-0ubuntu3.10) ...
#26 63.07 Setting up python3-pygments (2.11.2+dfsg-2ubuntu0.1) ...
#26 63.53 Setting up libz3-4:arm64 (4.8.12-1) ...
#26 63.54 Setting up libpfm4:arm64 (4.11.1+git32-gd0b85fb-1ubuntu0.1) ...
#26 63.54 Setting up vim-common (2:8.2.3995-1ubuntu2.24) ...
#26 63.55 Setting up libexpat1-dev:arm64 (2.4.7-1ubuntu0.6) ...
#26 63.56 Setting up libboost-regex1.74.0:arm64 (1.74.0-14ubuntu3) ...
#26 63.57 Setting up tree (2.0.2-1) ...
#26 63.57 Setting up libtinyxml2-9:arm64 (9.0.0+dfsg-3) ...
#26 63.58 Setting up binfmt-support (2.2.1-2) ...
#26 63.60 invoke-rc.d: could not determine current runlevel
#26 63.60 invoke-rc.d: policy-rc.d denied execution of restart.
#26 63.68 Created symlink /etc/systemd/system/multi-user.target.wants/binfmt-support.service → /lib/systemd/system/binfmt-support.service.
#26 63.69 Setting up icu-devtools (70.1-2) ...
#26 63.70 Setting up libpython3.10-dev:arm64 (3.10.12-1~22.04.10) ...
#26 63.71 Setting up libgc1:arm64 (1:8.0.6-1.1build1) ...
#26 63.71 Setting up libbabeltrace1:arm64 (1.5.8-2build1) ...
#26 63.72 Setting up cppcheck (2.7-1) ...
#26 63.73 Setting up libllvm14:arm64 (1:14.0.0-1ubuntu1.1) ...
#26 63.73 Setting up nano (6.2-1ubuntu0.1) ...
#26 63.75 update-alternatives: using /bin/nano to provide /usr/bin/editor (editor) in auto mode
#26 63.75 update-alternatives: warning: skip creation of /usr/share/man/man1/editor.1.gz because associated file /usr/share/man/man1/nano.1.gz (of link group editor) doesn't exist
#26 63.75 update-alternatives: using /bin/nano to provide /usr/bin/pico (pico) in auto mode
#26 63.75 update-alternatives: warning: skip creation of /usr/share/man/man1/pico.1.gz because associated file /usr/share/man/man1/nano.1.gz (of link group pico) doesn't exist
#26 63.76 Setting up python3.10-dev (3.10.12-1~22.04.10) ...
#26 63.77 Setting up libnl-3-200:arm64 (3.5.0-0.1) ...
#26 63.77 Setting up libjs-jquery (3.6.0+dfsg+~3.5.13-1) ...
#26 63.78 Setting up vim-runtime (2:8.2.3995-1ubuntu2.24) ...
#26 63.85 Setting up valgrind (1:3.18.1-1ubuntu2) ...
#26 63.86 Setting up llvm-14-linker-tools (1:14.0.0-1ubuntu1.1) ...
#26 63.88 Setting up python3-lib2to3 (3.10.8-1~22.04) ...
#26 63.95 Setting up libicu-dev:arm64 (70.1-2) ...
#26 63.96 Setting up libonig5:arm64 (*******-2build1) ...
#26 63.96 Setting up libsource-highlight4v5 (3.1.9-4.1build2) ...
#26 63.97 Setting up libjs-underscore (1.13.2~dfsg-2) ...
#26 63.97 Setting up llvm-14-tools (1:14.0.0-1ubuntu1.1) ...
#26 63.98 Setting up python3-distutils (3.10.8-1~22.04) ...
#26 64.24 Setting up libtinfo-dev:arm64 (6.3-2ubuntu0.1) ...
#26 64.25 Setting up libz3-dev:arm64 (4.8.12-1) ...
#26 64.26 Setting up vim (2:8.2.3995-1ubuntu2.24) ...
#26 64.26 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/vim (vim) in auto mode
#26 64.27 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/vimdiff (vimdiff) in auto mode
#26 64.27 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/rvim (rvim) in auto mode
#26 64.27 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/rview (rview) in auto mode
#26 64.27 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/vi (vi) in auto mode
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/da/man1/vi.1.gz because associated file /usr/share/man/da/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/de/man1/vi.1.gz because associated file /usr/share/man/de/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/vi.1.gz because associated file /usr/share/man/fr/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/it/man1/vi.1.gz because associated file /usr/share/man/it/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/ja/man1/vi.1.gz because associated file /usr/share/man/ja/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/pl/man1/vi.1.gz because associated file /usr/share/man/pl/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/ru/man1/vi.1.gz because associated file /usr/share/man/ru/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.28 update-alternatives: warning: skip creation of /usr/share/man/man1/vi.1.gz because associated file /usr/share/man/man1/vim.1.gz (of link group vi) doesn't exist
#26 64.29 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/view (view) in auto mode
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/da/man1/view.1.gz because associated file /usr/share/man/da/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/de/man1/view.1.gz because associated file /usr/share/man/de/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/view.1.gz because associated file /usr/share/man/fr/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/it/man1/view.1.gz because associated file /usr/share/man/it/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/ja/man1/view.1.gz because associated file /usr/share/man/ja/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/pl/man1/view.1.gz because associated file /usr/share/man/pl/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/ru/man1/view.1.gz because associated file /usr/share/man/ru/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/man1/view.1.gz because associated file /usr/share/man/man1/vim.1.gz (of link group view) doesn't exist
#26 64.29 update-alternatives: using /usr/bin/vim.basic to provide /usr/bin/ex (ex) in auto mode
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/da/man1/ex.1.gz because associated file /usr/share/man/da/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/de/man1/ex.1.gz because associated file /usr/share/man/de/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/fr/man1/ex.1.gz because associated file /usr/share/man/fr/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/it/man1/ex.1.gz because associated file /usr/share/man/it/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/ja/man1/ex.1.gz because associated file /usr/share/man/ja/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/pl/man1/ex.1.gz because associated file /usr/share/man/pl/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/ru/man1/ex.1.gz because associated file /usr/share/man/ru/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.29 update-alternatives: warning: skip creation of /usr/share/man/man1/ex.1.gz because associated file /usr/share/man/man1/vim.1.gz (of link group ex) doesn't exist
#26 64.30 Setting up libpython3-dev:arm64 (3.10.6-1~22.04.1) ...
#26 64.31 Setting up python3-setuptools (59.6.0-1.2ubuntu0.22.04.3) ...
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 54.23 [10/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o
#25 57.08 [11/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o
#25 60.73 [12/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o
#25 62.04 [13/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o
#25 ...

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 64.61 Setting up libjq1:arm64 (1.6-2.1ubuntu3.1) ...
#26 64.62 Setting up libclang1-14 (1:14.0.0-1ubuntu1.1) ...
#26 64.62 Setting up libobjc4:arm64 (13.1.0-8ubuntu1~22.04) ...
#26 64.63 Setting up python3-wheel (0.37.1-2ubuntu0.22.04.1) ...
#26 64.74 Setting up libxml2-dev:arm64 (2.9.13+dfsg-1ubuntu0.7) ...
#26 64.75 Setting up gdb (12.1-0ubuntu1~22.04.2) ...
#26 64.75 Setting up llvm-14-runtime (1:14.0.0-1ubuntu1.1) ...
#26 64.76 Setting up libclang-common-14-dev (1:14.0.0-1ubuntu1.1) ...
#26 64.77 Setting up libclang-cpp14 (1:14.0.0-1ubuntu1.1) ...
#26 64.77 Setting up python3-pip (22.0.2+dfsg-1ubuntu0.6) ...
#26 65.63 Setting up libjs-sphinxdoc (4.3.2-1) ...
#26 65.64 Setting up jq (1.6-2.1ubuntu3.1) ...
#26 65.65 Setting up libnl-genl-3-200:arm64 (3.5.0-0.1) ...
#26 65.65 Setting up clang-format-14 (1:14.0.0-1ubuntu1.1) ...
#26 65.66 Setting up llvm-14 (1:14.0.0-1ubuntu1.1) ...
#26 65.66 Setting up clang-format:arm64 (1:14.0-55~exp2) ...
#26 65.67 Setting up htop (3.0.5-7build2) ...
#26 65.67 Setting up libobjc-11-dev:arm64 (11.4.0-1ubuntu1~22.04) ...
#26 65.68 Setting up python3-dev (3.10.6-1~22.04.1) ...
#26 65.69 Setting up llvm-14-dev (1:14.0.0-1ubuntu1.1) ...
#26 65.70 Setting up clang-14 (1:14.0.0-1ubuntu1.1) ...
#26 65.70 Setting up clang-tools-14 (1:14.0.0-1ubuntu1.1) ...
#26 65.71 Setting up clang (1:14.0-55~exp2) ...
#26 65.72 Setting up clang-tidy-14 (1:14.0.0-1ubuntu1.1) ...
#26 65.73 Setting up clang-tools:arm64 (1:14.0-55~exp2) ...
#26 65.73 Setting up clang-tidy (1:14.0-55~exp2) ...
#26 65.75 Processing triggers for libc-bin (2.35-0ubuntu3.10) ...
#26 66.74 Collecting conan==2.0.*
#26 66.87   Downloading conan-2.0.17.tar.gz (396 kB)
#26 66.95      ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 396.3/396.3 KB 4.7 MB/s eta 0:00:00
#26 67.21   Installing build dependencies: started
#26 ...

#25 [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac
#25 67.46 [14/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/workflow_engine.cpp.o
#25 69.40 [15/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o
#25 69.40 /build/src/lib/core/pipeline.cpp: In member function 'void omop::core::ETLPipeline::transformation_worker()':
#25 69.40 /build/src/lib/core/pipeline.cpp:360:24: warning: unused variable 'original_batch_size' [-Wunused-variable]
#25 69.40   360 |                 size_t original_batch_size = batch.size();
#25 69.40       |                        ^~~~~~~~~~~~~~~~~~~
#25 69.40 ninja: build stopped: subcommand failed.
#25 ERROR: process "/bin/sh -c echo \"Building target: $BUILD_TARGET\" &&     case \"$BUILD_TARGET\" in         \"common\")             cmake --build build --target omop_common -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo \"Common unit tests target not found\";                 cmake --build build --target common_integration_tests -j$(nproc) || echo \"Common integration tests target not found\";             fi ;;         \"core\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo \"Core unit tests target not found\";                 cmake --build build --target core_integration_tests -j$(nproc) || echo \"Core integration tests target not found\";             fi ;;         \"cdm\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo \"CDM unit tests target not found\";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo \"CDM integration tests target not found\";             fi ;;         \"extract\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo \"Extract unit tests target not found\";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo \"Extract integration tests target not found\";             fi ;;         \"transform\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo \"Transform unit tests target not found\";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo \"Transform integration tests target not found\";             fi ;;         \"load\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo \"Load unit tests target not found\";                 cmake --build build --target load_integration_tests -j$(nproc) || echo \"Load integration tests target not found\";             fi ;;         \"service\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo \"Service unit tests target not found\";                 cmake --build build --target service_integration_tests -j$(nproc) || echo \"Service integration tests target not found\";             fi ;;         \"api\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo \"API service built\" &&             cmake --build build --target omop_microservice -j$(nproc) || echo \"Microservice built\" &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo \"API unit tests target not found\";                 cmake --build build --target api_integration_tests -j$(nproc) || echo \"API integration tests target not found\";             fi ;;         \"cli\")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ \"$ENABLE_TESTS\" = \"true\" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo \"CLI unit tests target not found\";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo \"CLI integration tests target not found\";             fi ;;         \"all\")             cmake --build build -j$(nproc) ;;         *)             echo \"Unknown build target: $BUILD_TARGET\" && exit 1 ;;     esac" did not complete successfully: exit code: 1

#26 [omop-etl-dev base  5/13] RUN if [ "true" = "true" ]; then         apt-get update && apt-get install -y         gdb         valgrind         clang         clang-tools         clang-format         clang-tidy         cppcheck         vim         nano         htop         tree         jq         python3         python3-pip         && rm -rf /var/lib/apt/lists/* &&         pip3 install conan==2.0.* pre-commit;     fi
#26 CANCELED
------
 > [omop-etl-api builder 4/6] RUN echo "Building target: all" &&     case "all" in         "common")             cmake --build build --target omop_common -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target common_unit_tests -j$(nproc) || echo "Common unit tests target not found";                 cmake --build build --target common_integration_tests -j$(nproc) || echo "Common integration tests target not found";             fi ;;         "core")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target core_unit_tests -j$(nproc) || echo "Core unit tests target not found";                 cmake --build build --target core_integration_tests -j$(nproc) || echo "Core integration tests target not found";             fi ;;         "cdm")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_cdm -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cdm_unit_tests -j$(nproc) || echo "CDM unit tests target not found";                 cmake --build build --target cdm_integration_tests -j$(nproc) || echo "CDM integration tests target not found";             fi ;;         "extract")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target extract_unit_tests -j$(nproc) || echo "Extract unit tests target not found";                 cmake --build build --target extract_integration_tests -j$(nproc) || echo "Extract integration tests target not found";             fi ;;         "transform")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target transform_unit_tests -j$(nproc) || echo "Transform unit tests target not found";                 cmake --build build --target transform_integration_tests -j$(nproc) || echo "Transform integration tests target not found";             fi ;;         "load")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target load_unit_tests -j$(nproc) || echo "Load unit tests target not found";                 cmake --build build --target load_integration_tests -j$(nproc) || echo "Load integration tests target not found";             fi ;;         "service")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target service_unit_tests -j$(nproc) || echo "Service unit tests target not found";                 cmake --build build --target service_integration_tests -j$(nproc) || echo "Service integration tests target not found";             fi ;;         "api")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_api_service -j$(nproc) || echo "API service built" &&             cmake --build build --target omop_microservice -j$(nproc) || echo "Microservice built" &&             if [ "True" = "true" ]; then                 cmake --build build --target api_unit_tests -j$(nproc) || echo "API unit tests target not found";                 cmake --build build --target api_integration_tests -j$(nproc) || echo "API integration tests target not found";             fi ;;         "cli")             cmake --build build --target omop_common -j$(nproc) &&             cmake --build build --target omop_core -j$(nproc) &&             cmake --build build --target omop_extract -j$(nproc) &&             cmake --build build --target omop_transform -j$(nproc) &&             cmake --build build --target omop_load -j$(nproc) &&             cmake --build build --target omop_service -j$(nproc) &&             cmake --build build --target omop_cli -j$(nproc) &&             if [ "True" = "true" ]; then                 cmake --build build --target cli_unit_tests -j$(nproc) || echo "CLI unit tests target not found";                 cmake --build build --target cli_integration_tests -j$(nproc) || echo "CLI integration tests target not found";             fi ;;         "all")             cmake --build build -j$(nproc) ;;         *)             echo "Unknown build target: all" && exit 1 ;;     esac:
57.08 [11/56] Building CXX object src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o
60.73 [12/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o
62.04 [13/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o
67.46 [14/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/workflow_engine.cpp.o
69.40 [15/56] Building CXX object src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o
69.40 /build/src/lib/core/pipeline.cpp: In member function 'void omop::core::ETLPipeline::transformation_worker()':
69.40 /build/src/lib/core/pipeline.cpp:360:24: warning: unused variable 'original_batch_size' [-Wunused-variable]
69.40   360 |                 size_t original_batch_size = batch.size();
69.40       |                        ^~~~~~~~~~~~~~~~~~~
69.40 ninja: build stopped: subcommand failed.
------
