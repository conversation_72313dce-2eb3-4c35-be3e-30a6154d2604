#include <gtest/gtest.h>
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>

#include "etl/etl_pipeline.h"
#include "etl/config_manager.h"
#include "database/database_manager.h"
#include "database/connection_pool.h"
#include "cdm/omop_cdm.h"
#include "utils/logger.h"
#include "utils/test_helpers.h"

namespace omop_etl {
namespace test {

class ComprehensiveOMOPETLTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize logger for testing
        Logger::getInstance().setLogLevel(LogLevel::DEBUG);
        
        // Set up test database connections
        setupTestDatabases();
        
        // Load comprehensive test configuration
        config_path_ = "tests/integration/test_data/yaml/comprehensive_e2e_config.yaml";
        ASSERT_TRUE(std::filesystem::exists(config_path_)) 
            << "Configuration file not found: " << config_path_;
        
        config_manager_ = std::make_unique<ConfigManager>();
        ASSERT_TRUE(config_manager_->loadConfig(config_path_))
            << "Failed to load configuration from: " << config_path_;
        
        // Initialize ETL pipeline
        pipeline_ = std::make_unique<ETLPipeline>(config_manager_.get());
        
        // Set up test data paths
        test_data_dir_ = "tests/integration/test_data/csv/";
        ASSERT_TRUE(std::filesystem::exists(test_data_dir_))
            << "Test data directory not found: " << test_data_dir_;
    }

    void TearDown() override {
        // Clean up test databases
        cleanupTestDatabases();
        
        // Reset pipeline and config
        pipeline_.reset();
        config_manager_.reset();
    }

    void setupTestDatabases() {
        // Create test source database
        source_db_config_ = {
            .host = "localhost",
            .port = 5432,
            .database = "omop_etl_test_source",
            .username = "test_user",
            .password = "test_password",
            .connection_timeout = 30,
            .max_connections = 10
        };
        
        // Create test target database
        target_db_config_ = {
            .host = "localhost",
            .port = 5432,
            .database = "omop_etl_test_target",
            .username = "test_user",
            .password = "test_password",
            .connection_timeout = 30,
            .max_connections = 10
        };
        
        // Initialize database connections
        source_db_manager_ = std::make_unique<DatabaseManager>(source_db_config_);
        target_db_manager_ = std::make_unique<DatabaseManager>(target_db_config_);
        
        // Create and populate source tables
        createSourceTables();
        populateSourceTables();
        
        // Create target OMOP CDM tables
        createTargetTables();
    }

    void createSourceTables() {
        auto connection = source_db_manager_->getConnection();
        
        // Create patients table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS patients (
                patient_id INTEGER PRIMARY KEY,
                birth_date DATE,
                gender VARCHAR(10),
                race VARCHAR(50),
                ethnicity VARCHAR(50),
                location_id INTEGER
            )
        )");
        
        // Create patient_enrollment table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS patient_enrollment (
                enrollment_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                enrollment_start_date DATE,
                enrollment_end_date DATE,
                enrollment_type VARCHAR(20)
            )
        )");
        
        // Create encounters table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS encounters (
                encounter_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_type VARCHAR(20),
                admission_date TIMESTAMP,
                discharge_date TIMESTAMP,
                visit_type_source VARCHAR(20),
                provider_id INTEGER,
                department_id INTEGER,
                admission_source VARCHAR(20),
                discharge_disposition VARCHAR(20)
            )
        )");
        
        // Create diagnoses table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS diagnoses (
                diagnosis_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                icd_code VARCHAR(20),
                diagnosis_date DATE,
                resolution_date DATE,
                diagnosis_type VARCHAR(20),
                condition_status VARCHAR(20)
            )
        )");
        
        // Create medications table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS medications (
                medication_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                drug_code VARCHAR(20),
                drug_name VARCHAR(255),
                start_date DATE,
                duration_days INTEGER,
                prescription_type VARCHAR(20),
                quantity DECIMAL(10,2),
                days_supply INTEGER,
                refills INTEGER,
                sig_text TEXT,
                route VARCHAR(20),
                provider_id INTEGER,
                dose_value DECIMAL(10,2),
                dose_unit VARCHAR(20)
            )
        )");
        
        // Create procedures table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS procedures (
                procedure_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                procedure_code VARCHAR(20),
                procedure_date TIMESTAMP,
                procedure_type VARCHAR(20),
                modifier_code VARCHAR(10),
                quantity INTEGER,
                provider_id INTEGER
            )
        )");
        
        // Create lab_results table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS lab_results (
                lab_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                loinc_code VARCHAR(20),
                lab_name VARCHAR(255),
                result_date TIMESTAMP,
                result_type VARCHAR(20),
                operator VARCHAR(5),
                numeric_result DECIMAL(15,6),
                text_result VARCHAR(100),
                unit VARCHAR(50),
                ref_range_low DECIMAL(15,6),
                ref_range_high DECIMAL(15,6),
                provider_id INTEGER
            )
        )");
        
        // Create clinical_observations table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS clinical_observations (
                observation_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                observation_code VARCHAR(20),
                observation_date TIMESTAMP,
                observation_type VARCHAR(20),
                value_as_number DECIMAL(15,6),
                value_as_string VARCHAR(255),
                value_as_concept VARCHAR(100),
                unit VARCHAR(50),
                provider_id INTEGER
            )
        )");
        
        // Create deaths table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS deaths (
                patient_id INTEGER PRIMARY KEY,
                death_date DATE,
                death_type VARCHAR(20),
                cause_of_death_code VARCHAR(20)
            )
        )");
        
        // Create clinical_notes table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS clinical_notes (
                note_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                note_date TIMESTAMP,
                note_type VARCHAR(50),
                note_class VARCHAR(50),
                note_title VARCHAR(255),
                note_text TEXT,
                encoding_type VARCHAR(20),
                language_code VARCHAR(10),
                provider_id INTEGER,
                note_source_value VARCHAR(100)
            )
        )");
    }

    void populateSourceTables() {
        // Load CSV data into source tables
        loadCSVData("patients", test_data_dir_ + "e2e_patients.csv");
        loadCSVData("patient_enrollment", test_data_dir_ + "e2e_patient_enrollment.csv");
        loadCSVData("encounters", test_data_dir_ + "e2e_encounters.csv");
        loadCSVData("diagnoses", test_data_dir_ + "e2e_diagnoses.csv");
        loadCSVData("medications", test_data_dir_ + "e2e_medications.csv");
        loadCSVData("procedures", test_data_dir_ + "e2e_procedures.csv");
        loadCSVData("lab_results", test_data_dir_ + "e2e_lab_results.csv");
        loadCSVData("clinical_observations", test_data_dir_ + "e2e_clinical_observations.csv");
        loadCSVData("deaths", test_data_dir_ + "e2e_deaths.csv");
        loadCSVData("clinical_notes", test_data_dir_ + "e2e_clinical_notes.csv");
    }

    void createTargetTables() {
        auto connection = target_db_manager_->getConnection();
        
        // Create OMOP CDM schema
        connection->execute("CREATE SCHEMA IF NOT EXISTS test_cdm");
        
        // Create core OMOP tables using CDM definitions
        OMOPCDMTableCreator table_creator(connection);
        table_creator.createPersonTable("test_cdm");
        table_creator.createObservationPeriodTable("test_cdm");
        table_creator.createVisitOccurrenceTable("test_cdm");
        table_creator.createConditionOccurrenceTable("test_cdm");
        table_creator.createDrugExposureTable("test_cdm");
        table_creator.createProcedureOccurrenceTable("test_cdm");
        table_creator.createMeasurementTable("test_cdm");
        table_creator.createObservationTable("test_cdm");
        table_creator.createDeathTable("test_cdm");
        table_creator.createNoteTable("test_cdm");
    }

    void loadCSVData(const std::string& table_name, const std::string& csv_file) {
        auto connection = source_db_manager_->getConnection();
        
        std::ifstream file(csv_file);
        ASSERT_TRUE(file.is_open()) << "Failed to open CSV file: " << csv_file;
        
        std::string line;
        bool first_line = true;
        std::vector<std::string> columns;
        
        while (std::getline(file, line)) {
            if (first_line) {
                columns = parseCSVLine(line);
                first_line = false;
                continue;
            }
            
            auto values = parseCSVLine(line);
            if (values.size() != columns.size()) {
                continue; // Skip malformed lines
            }
            
            // Build INSERT statement
            std::string sql = "INSERT INTO " + table_name + " (";
            for (size_t i = 0; i < columns.size(); ++i) {
                if (i > 0) sql += ", ";
                sql += columns[i];
            }
            sql += ") VALUES (";
            for (size_t i = 0; i < values.size(); ++i) {
                if (i > 0) sql += ", ";
                if (values[i].empty() || values[i] == "NULL") {
                    sql += "NULL";
                } else {
                    sql += "'" + values[i] + "'";
                }
            }
            sql += ")";
            
            try {
                connection->execute(sql);
            } catch (const std::exception& e) {
                // Log but continue with other records
                Logger::getInstance().warn("Failed to insert record: " + std::string(e.what()));
            }
        }
    }

    std::vector<std::string> parseCSVLine(const std::string& line) {
        std::vector<std::string> result;
        std::string current;
        bool in_quotes = false;
        
        for (char c : line) {
            if (c == '"') {
                in_quotes = !in_quotes;
            } else if (c == ',' && !in_quotes) {
                result.push_back(current);
                current.clear();
            } else {
                current += c;
            }
        }
        result.push_back(current);
        return result;
    }

    void cleanupTestDatabases() {
        if (source_db_manager_) {
            auto connection = source_db_manager_->getConnection();
            connection->execute("DROP SCHEMA IF EXISTS public CASCADE");
            connection->execute("CREATE SCHEMA public");
        }
        
        if (target_db_manager_) {
            auto connection = target_db_manager_->getConnection();
            connection->execute("DROP SCHEMA IF EXISTS test_cdm CASCADE");
        }
    }

    // Test data and configuration
    std::string config_path_;
    std::string test_data_dir_;
    
    // Database configurations
    DatabaseConfig source_db_config_;
    DatabaseConfig target_db_config_;
    
    // Components
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<ETLPipeline> pipeline_;
    std::unique_ptr<DatabaseManager> source_db_manager_;
    std::unique_ptr<DatabaseManager> target_db_manager_;
};

// ============================================================================
// EXTRACT STAGE TESTS
// ============================================================================

// Test individual extractor functionality
TEST_F(ComprehensiveOMOPETLTest, ExtractStage_DatabaseExtractor) {
    // Create and configure database extractor
    auto extractor = std::make_unique<extract::DatabaseExtractor>(source_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> extractor_config;
    extractor_config["table_name"] = std::string("patients");
    extractor_config["batch_size"] = size_t(10);
    extractor_config["columns"] = std::vector<std::string>{"patient_id", "birth_date", "gender", "race", "ethnicity"};

    core::ProcessingContext context;
    extractor->initialize(extractor_config, context);

    // Test extraction capabilities
    EXPECT_TRUE(extractor->has_more_data()) << "Extractor should have data available";
    EXPECT_EQ(extractor->get_type(), "database") << "Extractor type mismatch";

    // Extract first batch
    auto batch1 = extractor->extract_batch(10, context);
    EXPECT_GT(batch1.size(), 0) << "First batch should contain records";
    EXPECT_LE(batch1.size(), 10) << "Batch size should not exceed requested size";

    // Verify record structure
    if (!batch1.empty()) {
        const auto& first_record = batch1.getRecords()[0];
        EXPECT_TRUE(first_record.hasField("patient_id")) << "Record should have patient_id field";
        EXPECT_TRUE(first_record.hasField("gender")) << "Record should have gender field";
    }

    // Test batch extraction until completion
    size_t total_extracted = batch1.size();
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_extracted += batch.size();
        if (batch.empty()) break; // Safety check
    }

    EXPECT_GT(total_extracted, 50) << "Should extract reasonable number of records";

    // Get extraction statistics
    auto stats = extractor->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["records_extracted"]), 0) << "Statistics should show extracted records";

    extractor->finalize(context);
}

// Test CSV extractor functionality
TEST_F(ComprehensiveOMOPETLTest, ExtractStage_CSVExtractor) {
    auto extractor = std::make_unique<extract::CSVExtractor>();

    std::unordered_map<std::string, std::any> extractor_config;
    extractor_config["file_path"] = test_data_dir_ + "e2e_patients.csv";
    extractor_config["delimiter"] = std::string(",");
    extractor_config["has_header"] = true;
    extractor_config["batch_size"] = size_t(15);

    core::ProcessingContext context;
    extractor->initialize(extractor_config, context);

    EXPECT_TRUE(extractor->has_more_data()) << "CSV extractor should have data";
    EXPECT_EQ(extractor->get_type(), "csv") << "Extractor type should be CSV";

    // Extract and validate CSV data
    auto batch = extractor->extract_batch(15, context);
    EXPECT_GT(batch.size(), 0) << "Should extract records from CSV";

    // Verify CSV-specific field parsing
    if (!batch.empty()) {
        const auto& record = batch.getRecords()[0];
        EXPECT_TRUE(record.hasField("patient_id")) << "CSV record should have patient_id";
        EXPECT_TRUE(record.hasField("birth_date")) << "CSV record should have birth_date";

        // Test data type handling
        auto patient_id = record.getField("patient_id");
        EXPECT_TRUE(patient_id.has_value()) << "Patient ID should have value";
    }

    extractor->finalize(context);
}

// Test extraction error handling
TEST_F(ComprehensiveOMOPETLTest, ExtractStage_ErrorHandling) {
    auto extractor = std::make_unique<extract::DatabaseExtractor>(source_db_manager_->getConnection());

    // Test with invalid table name
    std::unordered_map<std::string, std::any> invalid_config;
    invalid_config["table_name"] = std::string("non_existent_table");
    invalid_config["batch_size"] = size_t(10);

    core::ProcessingContext context;

    EXPECT_THROW(extractor->initialize(invalid_config, context), std::exception)
        << "Should throw exception for invalid table";

    // Test with valid configuration but connection issues
    auto disconnected_extractor = std::make_unique<extract::DatabaseExtractor>(nullptr);
    std::unordered_map<std::string, std::any> valid_config;
    valid_config["table_name"] = std::string("patients");

    EXPECT_THROW(disconnected_extractor->initialize(valid_config, context), std::exception)
        << "Should handle connection errors gracefully";
}

// Test extraction performance and scalability
TEST_F(ComprehensiveOMOPETLTest, ExtractStage_Performance) {
    auto extractor = std::make_unique<extract::DatabaseExtractor>(source_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("encounters");
    config["batch_size"] = size_t(50);

    core::ProcessingContext context;
    extractor->initialize(config, context);

    auto start_time = std::chrono::high_resolution_clock::now();

    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(50, context);
        total_records += batch.size();
        if (batch.empty()) break;
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    EXPECT_GT(total_records, 0) << "Should extract records";
    EXPECT_LT(duration.count(), 5000) << "Extraction should complete within 5 seconds";

    // Calculate extraction rate
    double records_per_second = total_records / (duration.count() / 1000.0);
    EXPECT_GT(records_per_second, 10) << "Should maintain reasonable extraction rate";

    extractor->finalize(context);
}

// ============================================================================
// TRANSFORM STAGE TESTS
// ============================================================================

// Test individual transformer functionality
TEST_F(ComprehensiveOMOPETLTest, TransformStage_PersonTransformation) {
    // Create transformation engine for person table
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Load person mapping configuration
    std::unordered_map<std::string, std::any> transform_config;
    auto person_mapping = config_manager_->getTableMapping("person");
    ASSERT_TRUE(person_mapping.has_value()) << "Person mapping should be available";

    transform_config["mapping"] = person_mapping.value();
    transform_config["target_table"] = std::string("person");

    core::ProcessingContext context;
    transformer->initialize(transform_config, context);

    EXPECT_EQ(transformer->get_type(), "transformation_engine") << "Transformer type mismatch";

    // Create test source record
    core::Record source_record;
    source_record.setField("patient_id", 12345);
    source_record.setField("birth_date", std::string("1985-03-15"));
    source_record.setField("gender", std::string("M"));
    source_record.setField("race", std::string("White"));
    source_record.setField("ethnicity", std::string("Not Hispanic"));
    source_record.setField("location_id", 1001);

    // Test single record transformation
    auto transformed_record = transformer->transform(source_record, context);
    ASSERT_TRUE(transformed_record.has_value()) << "Transformation should succeed";

    // Verify OMOP CDM field mappings
    const auto& omop_record = transformed_record.value();
    EXPECT_TRUE(omop_record.hasField("person_id")) << "Should have person_id field";
    EXPECT_TRUE(omop_record.hasField("gender_concept_id")) << "Should have gender_concept_id field";
    EXPECT_TRUE(omop_record.hasField("year_of_birth")) << "Should have year_of_birth field";
    EXPECT_TRUE(omop_record.hasField("race_concept_id")) << "Should have race_concept_id field";
    EXPECT_TRUE(omop_record.hasField("ethnicity_concept_id")) << "Should have ethnicity_concept_id field";

    // Verify data transformations
    auto person_id = omop_record.getField("person_id");
    EXPECT_EQ(std::any_cast<int>(person_id), 12345) << "Person ID should be preserved";

    auto year_of_birth = omop_record.getField("year_of_birth");
    EXPECT_EQ(std::any_cast<int>(year_of_birth), 1985) << "Year should be extracted from birth date";

    auto gender_concept_id = omop_record.getField("gender_concept_id");
    EXPECT_EQ(std::any_cast<int>(gender_concept_id), 8507) << "Male should map to concept 8507";

    // Test validation
    auto validation_result = transformer->validate(omop_record);
    EXPECT_TRUE(validation_result.is_valid()) << "Transformed record should be valid";

    // Get transformation statistics
    auto stats = transformer->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["records_transformed"]), 0) << "Should show transformation count";
}

// Test batch transformation
TEST_F(ComprehensiveOMOPETLTest, TransformStage_BatchTransformation) {
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Configure for condition occurrence transformation
    std::unordered_map<std::string, std::any> transform_config;
    auto condition_mapping = config_manager_->getTableMapping("condition_occurrence");
    ASSERT_TRUE(condition_mapping.has_value()) << "Condition mapping should be available";

    transform_config["mapping"] = condition_mapping.value();
    transform_config["target_table"] = std::string("condition_occurrence");

    core::ProcessingContext context;
    transformer->initialize(transform_config, context);

    // Create batch of source records
    core::RecordBatch source_batch;
    for (int i = 1; i <= 5; ++i) {
        core::Record record;
        record.setField("diagnosis_id", 1000 + i);
        record.setField("patient_id", 100 + i);
        record.setField("encounter_id", 2000 + i);
        record.setField("icd_code", std::string("I25.10"));
        record.setField("diagnosis_date", std::string("2023-06-" + std::to_string(10 + i)));
        record.setField("diagnosis_type", std::string("PRIMARY"));
        record.setField("condition_status", std::string("ACTIVE"));
        source_batch.addRecord(std::move(record));
    }

    EXPECT_EQ(source_batch.size(), 5) << "Source batch should have 5 records";

    // Transform batch
    auto transformed_batch = transformer->transform_batch(source_batch, context);
    EXPECT_GT(transformed_batch.size(), 0) << "Should transform at least some records";
    EXPECT_LE(transformed_batch.size(), 5) << "Should not exceed source batch size";

    // Verify batch transformation results
    if (!transformed_batch.empty()) {
        const auto& first_transformed = transformed_batch.getRecords()[0];
        EXPECT_TRUE(first_transformed.hasField("condition_occurrence_id")) << "Should have condition_occurrence_id";
        EXPECT_TRUE(first_transformed.hasField("person_id")) << "Should have person_id";
        EXPECT_TRUE(first_transformed.hasField("condition_concept_id")) << "Should have condition_concept_id";
        EXPECT_TRUE(first_transformed.hasField("condition_start_date")) << "Should have condition_start_date";
    }

    // Verify statistics updated for batch
    auto stats = transformer->get_statistics();
    EXPECT_GE(std::any_cast<size_t>(stats["records_transformed"]), transformed_batch.size())
        << "Statistics should reflect batch transformation";
}

// Test transformation error handling
TEST_F(ComprehensiveOMOPETLTest, TransformStage_ErrorHandling) {
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Configure transformer
    std::unordered_map<std::string, std::any> transform_config;
    auto drug_mapping = config_manager_->getTableMapping("drug_exposure");
    ASSERT_TRUE(drug_mapping.has_value()) << "Drug mapping should be available";

    transform_config["mapping"] = drug_mapping.value();
    transform_config["target_table"] = std::string("drug_exposure");

    core::ProcessingContext context;
    transformer->initialize(transform_config, context);

    // Test with invalid/missing required fields
    core::Record invalid_record;
    invalid_record.setField("medication_id", 12345);
    // Missing required fields like patient_id, drug_code, etc.

    auto result = transformer->transform(invalid_record, context);
    // Should either return empty optional or record with default/null values
    if (result.has_value()) {
        // If transformation succeeds, validation should catch issues
        auto validation_result = transformer->validate(result.value());
        EXPECT_FALSE(validation_result.is_valid()) << "Invalid record should fail validation";
        EXPECT_GT(validation_result.errors().size(), 0) << "Should have validation errors";
    }

    // Test with invalid data types
    core::Record type_error_record;
    type_error_record.setField("medication_id", std::string("not_a_number")); // Should be numeric
    type_error_record.setField("patient_id", 123);
    type_error_record.setField("drug_code", std::string("12345"));
    type_error_record.setField("start_date", std::string("invalid_date"));

    // Should handle gracefully
    EXPECT_NO_THROW({
        auto result2 = transformer->transform(type_error_record, context);
    }) << "Should handle type errors gracefully";

    // Verify error statistics
    auto stats = transformer->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["transformation_errors"]), 0)
        << "Should track transformation errors";
}

// Test vocabulary mapping in transformations
TEST_F(ComprehensiveOMOPETLTest, TransformStage_VocabularyMapping) {
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Configure for procedure transformation (uses vocabulary mapping)
    std::unordered_map<std::string, std::any> transform_config;
    auto procedure_mapping = config_manager_->getTableMapping("procedure_occurrence");
    ASSERT_TRUE(procedure_mapping.has_value()) << "Procedure mapping should be available";

    transform_config["mapping"] = procedure_mapping.value();
    transform_config["target_table"] = std::string("procedure_occurrence");

    core::ProcessingContext context;
    transformer->initialize(transform_config, context);

    // Test with various procedure codes
    std::vector<std::pair<std::string, std::string>> test_codes = {
        {"99213", "CPT4"},      // Office visit
        {"45378", "CPT4"},      // Colonoscopy
        {"93000", "CPT4"},      // EKG
        {"INVALID", "CPT4"}     // Invalid code
    };

    for (const auto& [code, vocabulary] : test_codes) {
        core::Record source_record;
        source_record.setField("procedure_id", 12345);
        source_record.setField("patient_id", 67890);
        source_record.setField("encounter_id", 11111);
        source_record.setField("procedure_code", code);
        source_record.setField("procedure_date", std::string("2023-07-15 10:30:00"));
        source_record.setField("procedure_type", std::string("PRIMARY"));

        auto transformed = transformer->transform(source_record, context);
        if (transformed.has_value()) {
            const auto& omop_record = transformed.value();
            EXPECT_TRUE(omop_record.hasField("procedure_concept_id"))
                << "Should have procedure_concept_id for code: " << code;

            auto concept_id = omop_record.getField("procedure_concept_id");
            if (code != "INVALID") {
                EXPECT_GT(std::any_cast<int>(concept_id), 0)
                    << "Valid codes should map to positive concept IDs: " << code;
            } else {
                EXPECT_EQ(std::any_cast<int>(concept_id), 0)
                    << "Invalid codes should map to concept ID 0";
            }
        }
    }
}

// ============================================================================
// LOAD STAGE TESTS
// ============================================================================

// Test individual loader functionality
TEST_F(ComprehensiveOMOPETLTest, LoadStage_DatabaseLoader) {
    // Create database loader for OMOP target
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> loader_config;
    loader_config["target_table"] = std::string("person");
    loader_config["schema"] = std::string("test_cdm");
    loader_config["batch_size"] = size_t(10);
    loader_config["validate_foreign_keys"] = true;
    loader_config["enable_constraints"] = true;

    core::ProcessingContext context;
    loader->initialize(loader_config, context);

    EXPECT_EQ(loader->get_type(), "omop_database") << "Loader type should be omop_database";

    // Create test OMOP person record
    core::Record person_record;
    person_record.setField("person_id", 12345);
    person_record.setField("gender_concept_id", 8507); // Male
    person_record.setField("year_of_birth", 1985);
    person_record.setField("month_of_birth", 3);
    person_record.setField("day_of_birth", 15);
    person_record.setField("birth_datetime", std::string("1985-03-15 00:00:00"));
    person_record.setField("race_concept_id", 8527); // White
    person_record.setField("ethnicity_concept_id", 38003564); // Not Hispanic
    person_record.setField("location_id", 1001);
    person_record.setField("provider_id", std::any{}); // NULL
    person_record.setField("care_site_id", std::any{}); // NULL
    person_record.setField("person_source_value", std::string("PAT12345"));
    person_record.setField("gender_source_value", std::string("M"));
    person_record.setField("gender_source_concept_id", 0);
    person_record.setField("race_source_value", std::string("White"));
    person_record.setField("race_source_concept_id", 0);
    person_record.setField("ethnicity_source_value", std::string("Not Hispanic"));
    person_record.setField("ethnicity_source_concept_id", 0);

    // Test single record loading
    bool load_success = loader->load(person_record, context);
    EXPECT_TRUE(load_success) << "Should successfully load valid person record";

    // Test commit
    EXPECT_NO_THROW(loader->commit(context)) << "Should commit successfully";

    // Verify record was loaded
    auto connection = target_db_manager_->getConnection();
    auto result = connection->query("SELECT COUNT(*) as count FROM test_cdm.person WHERE person_id = 12345");
    EXPECT_EQ(result->getInt("count"), 1) << "Record should be present in database";

    // Get loading statistics
    auto stats = loader->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["records_loaded"]), 0) << "Should show loaded record count";

    loader->finalize(context);
}

// Test batch loading functionality
TEST_F(ComprehensiveOMOPETLTest, LoadStage_BatchLoading) {
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> loader_config;
    loader_config["target_table"] = std::string("visit_occurrence");
    loader_config["schema"] = std::string("test_cdm");
    loader_config["batch_size"] = size_t(5);
    loader_config["use_batch_insert"] = true;

    core::ProcessingContext context;
    loader->initialize(loader_config, context);

    // Create batch of visit records
    core::RecordBatch visit_batch;
    for (int i = 1; i <= 10; ++i) {
        core::Record visit_record;
        visit_record.setField("visit_occurrence_id", 20000 + i);
        visit_record.setField("person_id", 12345); // Reference existing person
        visit_record.setField("visit_concept_id", 9201); // Inpatient visit
        visit_record.setField("visit_start_date", std::string("2023-06-" + std::to_string(10 + i)));
        visit_record.setField("visit_start_datetime", std::string("2023-06-" + std::to_string(10 + i) + " 08:00:00"));
        visit_record.setField("visit_end_date", std::string("2023-06-" + std::to_string(12 + i)));
        visit_record.setField("visit_end_datetime", std::string("2023-06-" + std::to_string(12 + i) + " 16:00:00"));
        visit_record.setField("visit_type_concept_id", 32817); // EHR
        visit_record.setField("provider_id", std::any{}); // NULL
        visit_record.setField("care_site_id", std::any{}); // NULL
        visit_record.setField("visit_source_value", std::string("ENC" + std::to_string(20000 + i)));
        visit_record.setField("visit_source_concept_id", 0);
        visit_record.setField("admitted_from_concept_id", 0);
        visit_record.setField("admitted_from_source_value", std::string(""));
        visit_record.setField("discharge_to_concept_id", 0);
        visit_record.setField("discharge_to_source_value", std::string(""));
        visit_record.setField("preceding_visit_occurrence_id", std::any{}); // NULL

        visit_batch.addRecord(std::move(visit_record));
    }

    EXPECT_EQ(visit_batch.size(), 10) << "Batch should contain 10 records";

    // Test batch loading
    size_t loaded_count = loader->load_batch(visit_batch, context);
    EXPECT_EQ(loaded_count, 10) << "Should load all 10 records in batch";

    // Commit batch
    EXPECT_NO_THROW(loader->commit(context)) << "Should commit batch successfully";

    // Verify all records were loaded
    auto connection = target_db_manager_->getConnection();
    auto result = connection->query("SELECT COUNT(*) as count FROM test_cdm.visit_occurrence WHERE visit_occurrence_id BETWEEN 20001 AND 20010");
    EXPECT_EQ(result->getInt("count"), 10) << "All batch records should be in database";

    // Verify batch statistics
    auto stats = loader->get_statistics();
    EXPECT_GE(std::any_cast<size_t>(stats["records_loaded"]), 10) << "Statistics should reflect batch loading";
    EXPECT_GT(std::any_cast<size_t>(stats["batches_processed"]), 0) << "Should track batch count";

    loader->finalize(context);
}

// Test load error handling and rollback
TEST_F(ComprehensiveOMOPETLTest, LoadStage_ErrorHandlingAndRollback) {
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> loader_config;
    loader_config["target_table"] = std::string("condition_occurrence");
    loader_config["schema"] = std::string("test_cdm");
    loader_config["validate_foreign_keys"] = true;
    loader_config["continue_on_error"] = false;

    core::ProcessingContext context;
    loader->initialize(loader_config, context);

    // Test with invalid foreign key reference
    core::Record invalid_record;
    invalid_record.setField("condition_occurrence_id", 30001);
    invalid_record.setField("person_id", 99999); // Non-existent person
    invalid_record.setField("condition_concept_id", 201826); // Type 2 diabetes
    invalid_record.setField("condition_start_date", std::string("2023-07-01"));
    invalid_record.setField("condition_start_datetime", std::string("2023-07-01 10:00:00"));
    invalid_record.setField("condition_type_concept_id", 32817); // EHR
    invalid_record.setField("condition_status_concept_id", 0);
    invalid_record.setField("stop_reason", std::string(""));
    invalid_record.setField("provider_id", std::any{}); // NULL
    invalid_record.setField("visit_occurrence_id", std::any{}); // NULL
    invalid_record.setField("visit_detail_id", std::any{}); // NULL
    invalid_record.setField("condition_source_value", std::string("E11.9"));
    invalid_record.setField("condition_source_concept_id", 0);
    invalid_record.setField("condition_status_source_value", std::string(""));

    // Should fail due to foreign key constraint
    bool load_result = loader->load(invalid_record, context);
    EXPECT_FALSE(load_result) << "Should fail to load record with invalid foreign key";

    // Test rollback functionality
    EXPECT_NO_THROW(loader->rollback(context)) << "Should rollback failed transaction";

    // Verify no invalid record was persisted
    auto connection = target_db_manager_->getConnection();
    auto result = connection->query("SELECT COUNT(*) as count FROM test_cdm.condition_occurrence WHERE condition_occurrence_id = 30001");
    EXPECT_EQ(result->getInt("count"), 0) << "Invalid record should not be in database after rollback";

    // Test with valid record after rollback
    core::Record valid_record = invalid_record;
    valid_record.setField("person_id", 12345); // Valid person ID from previous test

    bool valid_load_result = loader->load(valid_record, context);
    EXPECT_TRUE(valid_load_result) << "Should successfully load valid record after rollback";

    loader->commit(context);
    loader->finalize(context);
}

// Test loader performance and throughput
TEST_F(ComprehensiveOMOPETLTest, LoadStage_Performance) {
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());

    std::unordered_map<std::string, std::any> loader_config;
    loader_config["target_table"] = std::string("measurement");
    loader_config["schema"] = std::string("test_cdm");
    loader_config["batch_size"] = size_t(100);
    loader_config["use_batch_insert"] = true;
    loader_config["disable_constraints"] = true; // For performance testing

    core::ProcessingContext context;
    loader->initialize(loader_config, context);

    // Create large batch for performance testing
    core::RecordBatch large_batch;
    const size_t test_record_count = 500;

    auto start_time = std::chrono::high_resolution_clock::now();

    for (size_t i = 1; i <= test_record_count; ++i) {
        core::Record measurement_record;
        measurement_record.setField("measurement_id", 40000 + static_cast<int>(i));
        measurement_record.setField("person_id", 12345);
        measurement_record.setField("measurement_concept_id", 3025315); // Systolic BP
        measurement_record.setField("measurement_date", std::string("2023-07-15"));
        measurement_record.setField("measurement_datetime", std::string("2023-07-15 09:00:00"));
        measurement_record.setField("measurement_time", std::string("09:00:00"));
        measurement_record.setField("measurement_type_concept_id", 32817); // EHR
        measurement_record.setField("operator_concept_id", 0);
        measurement_record.setField("value_as_number", 120.0 + (i % 40)); // Varying values
        measurement_record.setField("value_as_concept_id", std::any{}); // NULL
        measurement_record.setField("unit_concept_id", 8876); // mmHg
        measurement_record.setField("range_low", 90.0);
        measurement_record.setField("range_high", 140.0);
        measurement_record.setField("provider_id", std::any{}); // NULL
        measurement_record.setField("visit_occurrence_id", std::any{}); // NULL
        measurement_record.setField("visit_detail_id", std::any{}); // NULL
        measurement_record.setField("measurement_source_value", std::string("SBP"));
        measurement_record.setField("measurement_source_concept_id", 0);
        measurement_record.setField("unit_source_value", std::string("mmHg"));
        measurement_record.setField("value_source_value", std::to_string(120 + (i % 40)));

        large_batch.addRecord(std::move(measurement_record));
    }

    // Load the large batch
    size_t loaded_count = loader->load_batch(large_batch, context);
    loader->commit(context);

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    EXPECT_EQ(loaded_count, test_record_count) << "Should load all test records";
    EXPECT_LT(duration.count(), 10000) << "Should complete loading within 10 seconds";

    // Calculate loading rate
    double records_per_second = test_record_count / (duration.count() / 1000.0);
    EXPECT_GT(records_per_second, 50) << "Should maintain reasonable loading rate";

    // Verify performance statistics
    auto stats = loader->get_statistics();
    EXPECT_GE(std::any_cast<size_t>(stats["records_loaded"]), test_record_count)
        << "Statistics should reflect performance test";

    loader->finalize(context);
}

// ============================================================================
// ETL STAGE COMBINATION TESTS
// ============================================================================

// Test Extract + Transform combination
TEST_F(ComprehensiveOMOPETLTest, ETLCombination_ExtractTransform) {
    // Set up extractor
    auto extractor = std::make_unique<extract::DatabaseExtractor>(source_db_manager_->getConnection());
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["table_name"] = std::string("diagnoses");
    extract_config["batch_size"] = size_t(20);

    core::ProcessingContext extract_context;
    extractor->initialize(extract_config, extract_context);

    // Set up transformer
    auto transformer = std::make_unique<transform::TransformationEngine>();
    std::unordered_map<std::string, std::any> transform_config;
    auto condition_mapping = config_manager_->getTableMapping("condition_occurrence");
    ASSERT_TRUE(condition_mapping.has_value()) << "Condition mapping should be available";

    transform_config["mapping"] = condition_mapping.value();
    transform_config["target_table"] = std::string("condition_occurrence");

    core::ProcessingContext transform_context;
    transformer->initialize(transform_config, transform_context);

    // Test Extract -> Transform pipeline
    size_t total_extracted = 0;
    size_t total_transformed = 0;

    while (extractor->has_more_data()) {
        // Extract batch
        auto extracted_batch = extractor->extract_batch(20, extract_context);
        if (extracted_batch.empty()) break;

        total_extracted += extracted_batch.size();

        // Transform batch
        auto transformed_batch = transformer->transform_batch(extracted_batch, transform_context);
        total_transformed += transformed_batch.size();

        // Verify transformation quality
        if (!transformed_batch.empty()) {
            const auto& first_transformed = transformed_batch.getRecords()[0];
            EXPECT_TRUE(first_transformed.hasField("condition_occurrence_id"))
                << "Transformed record should have OMOP CDM fields";
            EXPECT_TRUE(first_transformed.hasField("person_id"))
                << "Should have person_id field";
            EXPECT_TRUE(first_transformed.hasField("condition_concept_id"))
                << "Should have condition_concept_id field";
        }
    }

    EXPECT_GT(total_extracted, 0) << "Should extract records";
    EXPECT_GT(total_transformed, 0) << "Should transform records";
    EXPECT_LE(total_transformed, total_extracted) << "Transformed count should not exceed extracted";

    // Verify statistics from both stages
    auto extract_stats = extractor->get_statistics();
    auto transform_stats = transformer->get_statistics();

    EXPECT_EQ(std::any_cast<size_t>(extract_stats["records_extracted"]), total_extracted)
        << "Extract statistics should match";
    EXPECT_EQ(std::any_cast<size_t>(transform_stats["records_transformed"]), total_transformed)
        << "Transform statistics should match";

    extractor->finalize(extract_context);
}

// Test Transform + Load combination
TEST_F(ComprehensiveOMOPETLTest, ETLCombination_TransformLoad) {
    // Set up transformer
    auto transformer = std::make_unique<transform::TransformationEngine>();
    std::unordered_map<std::string, std::any> transform_config;
    auto drug_mapping = config_manager_->getTableMapping("drug_exposure");
    ASSERT_TRUE(drug_mapping.has_value()) << "Drug mapping should be available";

    transform_config["mapping"] = drug_mapping.value();
    transform_config["target_table"] = std::string("drug_exposure");

    core::ProcessingContext transform_context;
    transformer->initialize(transform_config, transform_context);

    // Set up loader
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());
    std::unordered_map<std::string, std::any> load_config;
    load_config["target_table"] = std::string("drug_exposure");
    load_config["schema"] = std::string("test_cdm");
    load_config["batch_size"] = size_t(10);

    core::ProcessingContext load_context;
    loader->initialize(load_config, load_context);

    // Create source medication records
    core::RecordBatch source_batch;
    for (int i = 1; i <= 15; ++i) {
        core::Record med_record;
        med_record.setField("medication_id", 50000 + i);
        med_record.setField("patient_id", 12345);
        med_record.setField("encounter_id", 20001);
        med_record.setField("drug_code", std::string("207106"));
        med_record.setField("drug_name", std::string("Metformin"));
        med_record.setField("start_date", std::string("2023-07-01"));
        med_record.setField("duration_days", 30);
        med_record.setField("prescription_type", std::string("PRESCRIPTION"));
        med_record.setField("quantity", 60.0);
        med_record.setField("days_supply", 30);
        med_record.setField("refills", 2);
        med_record.setField("sig_text", std::string("Take twice daily with meals"));
        med_record.setField("route", std::string("ORAL"));
        med_record.setField("provider_id", 201);
        med_record.setField("dose_value", 500.0);
        med_record.setField("dose_unit", std::string("mg"));

        source_batch.addRecord(std::move(med_record));
    }

    // Test Transform -> Load pipeline
    auto transformed_batch = transformer->transform_batch(source_batch, transform_context);
    EXPECT_GT(transformed_batch.size(), 0) << "Should transform medication records";

    size_t loaded_count = loader->load_batch(transformed_batch, load_context);
    EXPECT_EQ(loaded_count, transformed_batch.size()) << "Should load all transformed records";

    loader->commit(load_context);

    // Verify records in database
    auto connection = target_db_manager_->getConnection();
    auto result = connection->query(
        "SELECT COUNT(*) as count FROM test_cdm.drug_exposure WHERE drug_exposure_id BETWEEN 50001 AND 50015"
    );
    EXPECT_EQ(result->getInt("count"), static_cast<int>(loaded_count))
        << "All transformed records should be loaded";

    // Verify OMOP CDM compliance
    auto omop_result = connection->query(R"(
        SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN drug_concept_id > 0 THEN 1 END) as mapped_drugs,
            COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_persons
        FROM test_cdm.drug_exposure
        WHERE drug_exposure_id BETWEEN 50001 AND 50015
    )");

    EXPECT_EQ(omop_result->getInt("total"), static_cast<int>(loaded_count)) << "Should have all records";
    EXPECT_GT(omop_result->getInt("mapped_drugs"), 0) << "Should have vocabulary mappings";
    EXPECT_EQ(omop_result->getInt("valid_persons"), static_cast<int>(loaded_count)) << "All should have person_id";

    loader->finalize(load_context);
}

// Test full Extract + Transform + Load pipeline
TEST_F(ComprehensiveOMOPETLTest, ETLCombination_FullPipeline) {
    // Set up complete ETL pipeline for procedure_occurrence
    auto extractor = std::make_unique<extract::DatabaseExtractor>(source_db_manager_->getConnection());
    auto transformer = std::make_unique<transform::TransformationEngine>();
    auto loader = std::make_unique<load::OmopDatabaseLoader>(target_db_manager_->getConnection());

    // Configure extractor
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["table_name"] = std::string("procedures");
    extract_config["batch_size"] = size_t(25);

    core::ProcessingContext extract_context;
    extractor->initialize(extract_config, extract_context);

    // Configure transformer
    std::unordered_map<std::string, std::any> transform_config;
    auto procedure_mapping = config_manager_->getTableMapping("procedure_occurrence");
    ASSERT_TRUE(procedure_mapping.has_value()) << "Procedure mapping should be available";

    transform_config["mapping"] = procedure_mapping.value();
    transform_config["target_table"] = std::string("procedure_occurrence");

    core::ProcessingContext transform_context;
    transformer->initialize(transform_config, transform_context);

    // Configure loader
    std::unordered_map<std::string, std::any> load_config;
    load_config["target_table"] = std::string("procedure_occurrence");
    load_config["schema"] = std::string("test_cdm");
    load_config["batch_size"] = size_t(15);

    core::ProcessingContext load_context;
    loader->initialize(load_config, load_context);

    // Execute full ETL pipeline
    size_t total_extracted = 0;
    size_t total_transformed = 0;
    size_t total_loaded = 0;

    auto pipeline_start = std::chrono::high_resolution_clock::now();

    while (extractor->has_more_data()) {
        // Extract
        auto extracted_batch = extractor->extract_batch(25, extract_context);
        if (extracted_batch.empty()) break;
        total_extracted += extracted_batch.size();

        // Transform
        auto transformed_batch = transformer->transform_batch(extracted_batch, transform_context);
        total_transformed += transformed_batch.size();

        // Load
        if (!transformed_batch.empty()) {
            size_t loaded_count = loader->load_batch(transformed_batch, load_context);
            total_loaded += loaded_count;
        }
    }

    // Commit all loaded data
    loader->commit(load_context);

    auto pipeline_end = std::chrono::high_resolution_clock::now();
    auto pipeline_duration = std::chrono::duration_cast<std::chrono::milliseconds>(pipeline_end - pipeline_start);

    // Verify pipeline results
    EXPECT_GT(total_extracted, 0) << "Should extract procedure records";
    EXPECT_GT(total_transformed, 0) << "Should transform procedure records";
    EXPECT_GT(total_loaded, 0) << "Should load procedure records";
    EXPECT_LE(total_transformed, total_extracted) << "Transform count should not exceed extract";
    EXPECT_LE(total_loaded, total_transformed) << "Load count should not exceed transform";

    // Verify data quality in target database
    auto connection = target_db_manager_->getConnection();
    auto quality_result = connection->query(R"(
        SELECT
            COUNT(*) as total_procedures,
            COUNT(CASE WHEN procedure_concept_id > 0 THEN 1 END) as mapped_procedures,
            COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_persons,
            COUNT(CASE WHEN procedure_date IS NOT NULL THEN 1 END) as valid_dates,
            AVG(CASE WHEN procedure_concept_id > 0 THEN 1.0 ELSE 0.0 END) as mapping_rate
        FROM test_cdm.procedure_occurrence
    )");

    EXPECT_EQ(quality_result->getInt("total_procedures"), static_cast<int>(total_loaded))
        << "Database should contain all loaded procedures";
    EXPECT_GT(quality_result->getInt("mapped_procedures"), 0)
        << "Should have vocabulary mappings";
    EXPECT_EQ(quality_result->getInt("valid_persons"), static_cast<int>(total_loaded))
        << "All procedures should have person_id";
    EXPECT_EQ(quality_result->getInt("valid_dates"), static_cast<int>(total_loaded))
        << "All procedures should have procedure_date";
    EXPECT_GE(quality_result->getDouble("mapping_rate"), 0.7)
        << "Should achieve at least 70% vocabulary mapping rate";

    // Verify performance
    EXPECT_LT(pipeline_duration.count(), 30000) << "Full pipeline should complete within 30 seconds";

    double records_per_second = total_loaded / (pipeline_duration.count() / 1000.0);
    EXPECT_GT(records_per_second, 5) << "Should maintain reasonable throughput";

    // Clean up
    extractor->finalize(extract_context);
    loader->finalize(load_context);

    Logger::getInstance().info("Full ETL pipeline processed " + std::to_string(total_loaded) +
                              " records in " + std::to_string(pipeline_duration.count()) + " ms");
}

// ============================================================================
// COMPREHENSIVE PIPELINE TESTS
// ============================================================================

// Test comprehensive ETL pipeline execution with all tables
TEST_F(ComprehensiveOMOPETLTest, ComprehensivePipeline_AllTables) {
    ASSERT_NE(pipeline_, nullptr);

    // Execute the complete ETL pipeline
    auto start_time = std::chrono::high_resolution_clock::now();

    bool success = pipeline_->execute();

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

    EXPECT_TRUE(success) << "ETL pipeline execution failed";

    // Verify all OMOP tables have data
    auto connection = target_db_manager_->getConnection();

    std::vector<std::string> omop_tables = {
        "person", "observation_period", "visit_occurrence",
        "condition_occurrence", "drug_exposure", "procedure_occurrence",
        "measurement", "observation", "note"
    };

    for (const auto& table : omop_tables) {
        auto result = connection->query("SELECT COUNT(*) as count FROM test_cdm." + table);
        EXPECT_GT(result->getInt("count"), 0) << "Table " << table << " should have records";
    }

    Logger::getInstance().info("Comprehensive ETL pipeline completed in " +
                              std::to_string(duration.count()) + " seconds");
}

// Test pipeline error recovery and resilience
TEST_F(ComprehensiveOMOPETLTest, ComprehensivePipeline_ErrorRecovery) {
    // Inject some invalid data to test error handling
    auto connection = source_db_manager_->getConnection();

    // Add invalid patient record
    connection->execute(R"(
        INSERT INTO patients (patient_id, birth_date, gender, race, ethnicity, location_id)
        VALUES (99999, NULL, NULL, 'Invalid Race', 'Invalid Ethnicity', NULL)
    )");

    // Add invalid encounter with future date
    connection->execute(R"(
        INSERT INTO encounters (encounter_id, patient_id, encounter_type, admission_date, discharge_date, provider_id)
        VALUES (99999, 99999, 'INVALID', '2030-01-01 00:00:00', '2030-01-02 00:00:00', NULL)
    )");

    // Execute pipeline - should handle errors gracefully
    bool success = pipeline_->execute();
    EXPECT_TRUE(success) << "Pipeline should complete despite some invalid records";

    // Verify error handling statistics
    auto connection_target = target_db_manager_->getConnection();

    // Check that valid records were still processed
    auto person_result = connection_target->query("SELECT COUNT(*) as count FROM test_cdm.person WHERE person_id != 99999");
    EXPECT_GT(person_result->getInt("count"), 50) << "Valid person records should be processed";

    // Check that invalid records were filtered out or handled
    auto invalid_person_result = connection_target->query("SELECT COUNT(*) as count FROM test_cdm.person WHERE person_id = 99999");
    EXPECT_EQ(invalid_person_result->getInt("count"), 0) << "Invalid person record should be filtered out";

    // Clean up test data
    connection->execute("DELETE FROM encounters WHERE encounter_id = 99999");
    connection->execute("DELETE FROM patients WHERE patient_id = 99999");
}

// Test pipeline performance with concurrent processing
TEST_F(ComprehensiveOMOPETLTest, ComprehensivePipeline_ConcurrentProcessing) {
    // Configure pipeline for concurrent processing
    auto concurrent_config = *config_manager_;
    // Assume configuration supports parallel processing settings

    auto concurrent_pipeline = std::make_unique<ETLPipeline>(&concurrent_config);

    auto start_time = std::chrono::high_resolution_clock::now();

    bool success = concurrent_pipeline->execute();

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    EXPECT_TRUE(success) << "Concurrent pipeline execution should succeed";
    EXPECT_LT(duration.count(), 45000) << "Concurrent processing should complete within 45 seconds";

    // Verify data integrity with concurrent processing
    auto connection = target_db_manager_->getConnection();

    // Check for data consistency
    auto consistency_result = connection->query(R"(
        SELECT
            (SELECT COUNT(*) FROM test_cdm.person) as person_count,
            (SELECT COUNT(DISTINCT person_id) FROM test_cdm.visit_occurrence) as visit_persons,
            (SELECT COUNT(DISTINCT person_id) FROM test_cdm.condition_occurrence) as condition_persons
    )");

    int person_count = consistency_result->getInt("person_count");
    int visit_persons = consistency_result->getInt("visit_persons");
    int condition_persons = consistency_result->getInt("condition_persons");

    EXPECT_GT(person_count, 0) << "Should have person records";
    EXPECT_LE(visit_persons, person_count) << "Visit persons should not exceed total persons";
    EXPECT_LE(condition_persons, person_count) << "Condition persons should not exceed total persons";

    Logger::getInstance().info("Concurrent pipeline processed data in " +
                              std::to_string(duration.count()) + " milliseconds");
}

// Test data validation and quality checks
TEST_F(ComprehensiveOMOPETLTest, DataValidationAndQualityChecks) {
    // Execute ETL pipeline first
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Verify person count matches source
    auto person_count_result = connection->query(
        "SELECT COUNT(*) as target_count FROM test_cdm.person"
    );
    auto source_count_result = source_db_manager_->getConnection()->query(
        "SELECT COUNT(*) as source_count FROM patients WHERE birth_date IS NOT NULL AND gender IS NOT NULL"
    );

    EXPECT_GT(person_count_result->getInt("target_count"), 0) << "No persons loaded";
    EXPECT_EQ(person_count_result->getInt("target_count"),
              source_count_result->getInt("source_count")) << "Person count mismatch";

    // Test 2: Check for orphan visits
    auto orphan_visits_result = connection->query(R"(
        SELECT COUNT(*) as orphan_count
        FROM test_cdm.visit_occurrence v
        LEFT JOIN test_cdm.person p ON v.person_id = p.person_id
        WHERE p.person_id IS NULL
    )");
    EXPECT_EQ(orphan_visits_result->getInt("orphan_count"), 0) << "Found orphan visits";

    // Test 3: Verify date consistency
    auto date_consistency_result = connection->query(R"(
        SELECT COUNT(*) as invalid_dates
        FROM test_cdm.visit_occurrence
        WHERE visit_start_date > visit_end_date
    )");
    EXPECT_EQ(date_consistency_result->getInt("invalid_dates"), 0) << "Found invalid date ranges";

    // Test 4: Birth date validation
    auto birth_date_result = connection->query(R"(
        SELECT COUNT(*) as invalid_birth_dates
        FROM test_cdm.person
        WHERE year_of_birth < 1900 OR year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE)
    )");
    EXPECT_LE(birth_date_result->getInt("invalid_birth_dates"), 5) << "Too many invalid birth dates";

    // Test 5: Referential integrity check
    auto referential_integrity_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence co
           LEFT JOIN test_cdm.person p ON co.person_id = p.person_id
           WHERE p.person_id IS NULL) +
          (SELECT COUNT(*) FROM test_cdm.drug_exposure de
           LEFT JOIN test_cdm.person p ON de.person_id = p.person_id
           WHERE p.person_id IS NULL) +
          (SELECT COUNT(*) FROM test_cdm.procedure_occurrence po
           LEFT JOIN test_cdm.person p ON po.person_id = p.person_id
           WHERE p.person_id IS NULL) as orphan_records
    )");
    EXPECT_EQ(referential_integrity_result->getInt("orphan_records"), 0) << "Referential integrity violations found";
}

// Test vocabulary mapping coverage
TEST_F(ComprehensiveOMOPETLTest, VocabularyMappingCoverage) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test condition concept mapping coverage
    auto condition_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_conditions,
          COUNT(CASE WHEN condition_concept_id > 0 THEN 1 END) as mapped_conditions,
          ROUND(
            COUNT(CASE WHEN condition_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.condition_occurrence
    )");

    EXPECT_GT(condition_mapping_result->getInt("total_conditions"), 0) << "No conditions loaded";
    EXPECT_GE(condition_mapping_result->getDouble("mapping_percentage"), 80.0)
        << "Condition mapping coverage below 80%";

    // Test drug concept mapping coverage
    auto drug_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_drugs,
          COUNT(CASE WHEN drug_concept_id > 0 THEN 1 END) as mapped_drugs,
          ROUND(
            COUNT(CASE WHEN drug_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.drug_exposure
    )");

    EXPECT_GT(drug_mapping_result->getInt("total_drugs"), 0) << "No drugs loaded";
    EXPECT_GE(drug_mapping_result->getDouble("mapping_percentage"), 75.0)
        << "Drug mapping coverage below 75%";

    // Test procedure concept mapping coverage
    auto procedure_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_procedures,
          COUNT(CASE WHEN procedure_concept_id > 0 THEN 1 END) as mapped_procedures,
          ROUND(
            COUNT(CASE WHEN procedure_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.procedure_occurrence
    )");

    EXPECT_GT(procedure_mapping_result->getInt("total_procedures"), 0) << "No procedures loaded";
    EXPECT_GE(procedure_mapping_result->getDouble("mapping_percentage"), 70.0)
        << "Procedure mapping coverage below 70%";
}

// Test data transformation accuracy
TEST_F(ComprehensiveOMOPETLTest, DataTransformationAccuracy) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto source_connection = source_db_manager_->getConnection();
    auto target_connection = target_db_manager_->getConnection();

    // Test 1: Verify gender mapping
    auto gender_mapping_result = target_connection->query(R"(
        SELECT
          gender_concept_id,
          COUNT(*) as count
        FROM test_cdm.person
        GROUP BY gender_concept_id
        ORDER BY gender_concept_id
    )");

    // Should have valid gender concept IDs (8507 for Male, 8532 for Female)
    bool found_male = false, found_female = false;
    while (gender_mapping_result->next()) {
        int gender_concept_id = gender_mapping_result->getInt("gender_concept_id");
        if (gender_concept_id == 8507) found_male = true;
        if (gender_concept_id == 8532) found_female = true;
    }
    EXPECT_TRUE(found_male || found_female) << "No valid gender mappings found";

    // Test 2: Verify date transformations
    auto date_transform_result = target_connection->query(R"(
        SELECT
          COUNT(*) as total_visits,
          COUNT(CASE WHEN visit_start_date IS NOT NULL THEN 1 END) as valid_start_dates,
          COUNT(CASE WHEN visit_end_date IS NOT NULL THEN 1 END) as valid_end_dates
        FROM test_cdm.visit_occurrence
    )");

    EXPECT_GT(date_transform_result->getInt("total_visits"), 0) << "No visits loaded";
    EXPECT_EQ(date_transform_result->getInt("valid_start_dates"),
              date_transform_result->getInt("total_visits")) << "Missing visit start dates";

    // Test 3: Verify numeric transformations in measurements
    auto numeric_transform_result = target_connection->query(R"(
        SELECT
          COUNT(*) as total_measurements,
          COUNT(CASE WHEN value_as_number IS NOT NULL THEN 1 END) as numeric_values,
          AVG(value_as_number) as avg_value,
          MIN(value_as_number) as min_value,
          MAX(value_as_number) as max_value
        FROM test_cdm.measurement
        WHERE value_as_number IS NOT NULL
    )");

    if (numeric_transform_result->getInt("total_measurements") > 0) {
        EXPECT_GT(numeric_transform_result->getInt("numeric_values"), 0) << "No numeric measurements";
        EXPECT_GT(numeric_transform_result->getDouble("avg_value"), 0) << "Invalid average measurement value";
    }
}

// Test error handling and data quality scenarios
TEST_F(ComprehensiveOMOPETLTest, ErrorHandlingAndDataQuality) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Check for duplicate records
    auto duplicate_check_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) - COUNT(DISTINCT person_id) FROM test_cdm.person) +
          (SELECT COUNT(*) - COUNT(DISTINCT visit_occurrence_id) FROM test_cdm.visit_occurrence) +
          (SELECT COUNT(*) - COUNT(DISTINCT condition_occurrence_id) FROM test_cdm.condition_occurrence) as duplicate_count
    )");
    EXPECT_EQ(duplicate_check_result->getInt("duplicate_count"), 0) << "Duplicate records found";

    // Test 2: Verify measurement range validation
    auto measurement_range_result = connection->query(R"(
        SELECT COUNT(*) as out_of_range_measurements
        FROM test_cdm.measurement
        WHERE (range_low IS NOT NULL AND value_as_number < range_low * 0.1)
           OR (range_high IS NOT NULL AND value_as_number > range_high * 10)
    )");
    EXPECT_LE(measurement_range_result->getInt("out_of_range_measurements"), 10)
        << "Too many out-of-range measurements";

    // Test 3: Drug exposure validation
    auto drug_validation_result = connection->query(R"(
        SELECT COUNT(*) as invalid_drugs
        FROM test_cdm.drug_exposure
        WHERE drug_exposure_start_date > drug_exposure_end_date
           OR quantity <= 0
           OR days_supply <= 0
    )");
    EXPECT_EQ(drug_validation_result->getInt("invalid_drugs"), 0) << "Invalid drug exposures found";

    // Test 4: Check for reasonable age distributions
    auto age_distribution_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN year_of_birth BETWEEN 1920 AND 2020 THEN 1 END) as reasonable_ages,
          AVG(EXTRACT(YEAR FROM CURRENT_DATE) - year_of_birth) as avg_age
        FROM test_cdm.person
    )");

    EXPECT_GT(age_distribution_result->getInt("total_persons"), 0) << "No persons loaded";
    EXPECT_GE(age_distribution_result->getDouble("reasonable_ages") /
              age_distribution_result->getDouble("total_persons"), 0.9)
        << "Too many unreasonable ages";
    EXPECT_BETWEEN(age_distribution_result->getDouble("avg_age"), 20.0, 80.0)
        << "Average age outside reasonable range";
}

// Test performance and scalability
TEST_F(ComprehensiveOMOPETLTest, PerformanceAndScalability) {
    auto start_time = std::chrono::high_resolution_clock::now();

    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // Performance expectations (adjust based on test data size)
    EXPECT_LT(duration.count(), 60000) << "ETL pipeline took longer than 60 seconds";

    auto connection = target_db_manager_->getConnection();

    // Test record counts meet expectations
    auto record_counts_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) FROM test_cdm.person) as person_count,
          (SELECT COUNT(*) FROM test_cdm.visit_occurrence) as visit_count,
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence) as condition_count,
          (SELECT COUNT(*) FROM test_cdm.drug_exposure) as drug_count,
          (SELECT COUNT(*) FROM test_cdm.procedure_occurrence) as procedure_count,
          (SELECT COUNT(*) FROM test_cdm.measurement) as measurement_count,
          (SELECT COUNT(*) FROM test_cdm.observation) as observation_count
    )");

    // Verify minimum record counts (adjust based on test data)
    EXPECT_GE(record_counts_result->getInt("person_count"), 50) << "Insufficient person records";
    EXPECT_GE(record_counts_result->getInt("visit_count"), 50) << "Insufficient visit records";
    EXPECT_GE(record_counts_result->getInt("condition_count"), 50) << "Insufficient condition records";
    EXPECT_GE(record_counts_result->getInt("drug_count"), 40) << "Insufficient drug records";
    EXPECT_GE(record_counts_result->getInt("procedure_count"), 60) << "Insufficient procedure records";
    EXPECT_GE(record_counts_result->getInt("measurement_count"), 60) << "Insufficient measurement records";
    EXPECT_GE(record_counts_result->getInt("observation_count"), 60) << "Insufficient observation records";

    Logger::getInstance().info("Performance test completed in " +
                              std::to_string(duration.count()) + " milliseconds");
}

// Test specific OMOP CDM compliance
TEST_F(ComprehensiveOMOPETLTest, OMOPCDMCompliance) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Verify required fields are populated
    auto required_fields_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_ids,
          COUNT(CASE WHEN gender_concept_id IS NOT NULL THEN 1 END) as valid_genders,
          COUNT(CASE WHEN year_of_birth IS NOT NULL THEN 1 END) as valid_birth_years
        FROM test_cdm.person
    )");

    EXPECT_EQ(required_fields_result->getInt("valid_person_ids"),
              required_fields_result->getInt("total_persons")) << "Missing person IDs";
    EXPECT_EQ(required_fields_result->getInt("valid_genders"),
              required_fields_result->getInt("total_persons")) << "Missing gender concepts";
    EXPECT_EQ(required_fields_result->getInt("valid_birth_years"),
              required_fields_result->getInt("total_persons")) << "Missing birth years";

    // Test 2: Verify visit occurrence requirements
    auto visit_requirements_result = connection->query(R"(
        SELECT
          COUNT(*) as total_visits,
          COUNT(CASE WHEN visit_occurrence_id IS NOT NULL THEN 1 END) as valid_visit_ids,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_refs,
          COUNT(CASE WHEN visit_concept_id IS NOT NULL THEN 1 END) as valid_visit_concepts,
          COUNT(CASE WHEN visit_start_date IS NOT NULL THEN 1 END) as valid_start_dates
        FROM test_cdm.visit_occurrence
    )");

    EXPECT_EQ(visit_requirements_result->getInt("valid_visit_ids"),
              visit_requirements_result->getInt("total_visits")) << "Missing visit IDs";
    EXPECT_EQ(visit_requirements_result->getInt("valid_person_refs"),
              visit_requirements_result->getInt("total_visits")) << "Missing person references";
    EXPECT_EQ(visit_requirements_result->getInt("valid_start_dates"),
              visit_requirements_result->getInt("total_visits")) << "Missing visit start dates";

    // Test 3: Verify condition occurrence requirements
    auto condition_requirements_result = connection->query(R"(
        SELECT
          COUNT(*) as total_conditions,
          COUNT(CASE WHEN condition_occurrence_id IS NOT NULL THEN 1 END) as valid_condition_ids,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_refs,
          COUNT(CASE WHEN condition_start_date IS NOT NULL THEN 1 END) as valid_start_dates
        FROM test_cdm.condition_occurrence
    )");

    EXPECT_EQ(condition_requirements_result->getInt("valid_condition_ids"),
              condition_requirements_result->getInt("total_conditions")) << "Missing condition IDs";
    EXPECT_EQ(condition_requirements_result->getInt("valid_person_refs"),
              condition_requirements_result->getInt("total_conditions")) << "Missing person references";
    EXPECT_EQ(condition_requirements_result->getInt("valid_start_dates"),
              condition_requirements_result->getInt("total_conditions")) << "Missing condition start dates";
}

// Test edge cases and boundary conditions
TEST_F(ComprehensiveOMOPETLTest, EdgeCasesAndBoundaryConditions) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Handle NULL and empty values appropriately
    auto null_handling_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN race_concept_id = 0 THEN 1 END) as unmapped_race,
          COUNT(CASE WHEN ethnicity_concept_id = 0 THEN 1 END) as unmapped_ethnicity
        FROM test_cdm.person
    )");

    // Allow some unmapped values but not all
    EXPECT_LT(null_handling_result->getDouble("unmapped_race") /
              null_handling_result->getDouble("total_persons"), 0.5)
        << "Too many unmapped race values";

    // Test 2: Verify handling of extreme dates
    auto extreme_dates_result = connection->query(R"(
        SELECT
          COUNT(*) as future_visits,
          COUNT(*) as very_old_visits
        FROM test_cdm.visit_occurrence
        WHERE visit_start_date > CURRENT_DATE + INTERVAL '1 year'
           OR visit_start_date < DATE '1900-01-01'
    )");

    EXPECT_EQ(extreme_dates_result->getInt("future_visits"), 0) << "Found future visit dates";
    EXPECT_LE(extreme_dates_result->getInt("very_old_visits"), 5) << "Too many very old visit dates";

    // Test 3: Verify measurement value ranges
    auto measurement_extremes_result = connection->query(R"(
        SELECT
          COUNT(*) as total_measurements,
          COUNT(CASE WHEN value_as_number < 0 AND measurement_concept_id IN (3025315, 3012888) THEN 1 END) as negative_vitals,
          COUNT(CASE WHEN value_as_number > 1000 AND measurement_concept_id IN (3025315, 3012888) THEN 1 END) as extreme_vitals
        FROM test_cdm.measurement
        WHERE value_as_number IS NOT NULL
    )");

    // Should have minimal negative vital signs or extreme values
    EXPECT_LE(measurement_extremes_result->getInt("negative_vitals"), 2)
        << "Too many negative vital signs";
    EXPECT_LE(measurement_extremes_result->getInt("extreme_vitals"), 5)
        << "Too many extreme vital sign values";
}

} // namespace test
} // namespace omop_etl
