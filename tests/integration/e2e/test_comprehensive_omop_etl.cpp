#include <gtest/gtest.h>
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>

#include "etl/etl_pipeline.h"
#include "etl/config_manager.h"
#include "database/database_manager.h"
#include "database/connection_pool.h"
#include "cdm/omop_cdm.h"
#include "utils/logger.h"
#include "utils/test_helpers.h"

namespace omop_etl {
namespace test {

class ComprehensiveOMOPETLTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize logger for testing
        Logger::getInstance().setLogLevel(LogLevel::DEBUG);
        
        // Set up test database connections
        setupTestDatabases();
        
        // Load comprehensive test configuration
        config_path_ = "tests/integration/test_data/yaml/comprehensive_e2e_config.yaml";
        ASSERT_TRUE(std::filesystem::exists(config_path_)) 
            << "Configuration file not found: " << config_path_;
        
        config_manager_ = std::make_unique<ConfigManager>();
        ASSERT_TRUE(config_manager_->loadConfig(config_path_))
            << "Failed to load configuration from: " << config_path_;
        
        // Initialize ETL pipeline
        pipeline_ = std::make_unique<ETLPipeline>(config_manager_.get());
        
        // Set up test data paths
        test_data_dir_ = "tests/integration/test_data/csv/";
        ASSERT_TRUE(std::filesystem::exists(test_data_dir_))
            << "Test data directory not found: " << test_data_dir_;
    }

    void TearDown() override {
        // Clean up test databases
        cleanupTestDatabases();
        
        // Reset pipeline and config
        pipeline_.reset();
        config_manager_.reset();
    }

    void setupTestDatabases() {
        // Create test source database
        source_db_config_ = {
            .host = "localhost",
            .port = 5432,
            .database = "omop_etl_test_source",
            .username = "test_user",
            .password = "test_password",
            .connection_timeout = 30,
            .max_connections = 10
        };
        
        // Create test target database
        target_db_config_ = {
            .host = "localhost",
            .port = 5432,
            .database = "omop_etl_test_target",
            .username = "test_user",
            .password = "test_password",
            .connection_timeout = 30,
            .max_connections = 10
        };
        
        // Initialize database connections
        source_db_manager_ = std::make_unique<DatabaseManager>(source_db_config_);
        target_db_manager_ = std::make_unique<DatabaseManager>(target_db_config_);
        
        // Create and populate source tables
        createSourceTables();
        populateSourceTables();
        
        // Create target OMOP CDM tables
        createTargetTables();
    }

    void createSourceTables() {
        auto connection = source_db_manager_->getConnection();
        
        // Create patients table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS patients (
                patient_id INTEGER PRIMARY KEY,
                birth_date DATE,
                gender VARCHAR(10),
                race VARCHAR(50),
                ethnicity VARCHAR(50),
                location_id INTEGER
            )
        )");
        
        // Create patient_enrollment table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS patient_enrollment (
                enrollment_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                enrollment_start_date DATE,
                enrollment_end_date DATE,
                enrollment_type VARCHAR(20)
            )
        )");
        
        // Create encounters table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS encounters (
                encounter_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_type VARCHAR(20),
                admission_date TIMESTAMP,
                discharge_date TIMESTAMP,
                visit_type_source VARCHAR(20),
                provider_id INTEGER,
                department_id INTEGER,
                admission_source VARCHAR(20),
                discharge_disposition VARCHAR(20)
            )
        )");
        
        // Create diagnoses table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS diagnoses (
                diagnosis_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                icd_code VARCHAR(20),
                diagnosis_date DATE,
                resolution_date DATE,
                diagnosis_type VARCHAR(20),
                condition_status VARCHAR(20)
            )
        )");
        
        // Create medications table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS medications (
                medication_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                drug_code VARCHAR(20),
                drug_name VARCHAR(255),
                start_date DATE,
                duration_days INTEGER,
                prescription_type VARCHAR(20),
                quantity DECIMAL(10,2),
                days_supply INTEGER,
                refills INTEGER,
                sig_text TEXT,
                route VARCHAR(20),
                provider_id INTEGER,
                dose_value DECIMAL(10,2),
                dose_unit VARCHAR(20)
            )
        )");
        
        // Create procedures table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS procedures (
                procedure_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                procedure_code VARCHAR(20),
                procedure_date TIMESTAMP,
                procedure_type VARCHAR(20),
                modifier_code VARCHAR(10),
                quantity INTEGER,
                provider_id INTEGER
            )
        )");
        
        // Create lab_results table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS lab_results (
                lab_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                loinc_code VARCHAR(20),
                lab_name VARCHAR(255),
                result_date TIMESTAMP,
                result_type VARCHAR(20),
                operator VARCHAR(5),
                numeric_result DECIMAL(15,6),
                text_result VARCHAR(100),
                unit VARCHAR(50),
                ref_range_low DECIMAL(15,6),
                ref_range_high DECIMAL(15,6),
                provider_id INTEGER
            )
        )");
        
        // Create clinical_observations table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS clinical_observations (
                observation_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                observation_code VARCHAR(20),
                observation_date TIMESTAMP,
                observation_type VARCHAR(20),
                value_as_number DECIMAL(15,6),
                value_as_string VARCHAR(255),
                value_as_concept VARCHAR(100),
                unit VARCHAR(50),
                provider_id INTEGER
            )
        )");
        
        // Create deaths table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS deaths (
                patient_id INTEGER PRIMARY KEY,
                death_date DATE,
                death_type VARCHAR(20),
                cause_of_death_code VARCHAR(20)
            )
        )");
        
        // Create clinical_notes table
        connection->execute(R"(
            CREATE TABLE IF NOT EXISTS clinical_notes (
                note_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                encounter_id INTEGER,
                note_date TIMESTAMP,
                note_type VARCHAR(50),
                note_class VARCHAR(50),
                note_title VARCHAR(255),
                note_text TEXT,
                encoding_type VARCHAR(20),
                language_code VARCHAR(10),
                provider_id INTEGER,
                note_source_value VARCHAR(100)
            )
        )");
    }

    void populateSourceTables() {
        // Load CSV data into source tables
        loadCSVData("patients", test_data_dir_ + "e2e_patients.csv");
        loadCSVData("patient_enrollment", test_data_dir_ + "e2e_patient_enrollment.csv");
        loadCSVData("encounters", test_data_dir_ + "e2e_encounters.csv");
        loadCSVData("diagnoses", test_data_dir_ + "e2e_diagnoses.csv");
        loadCSVData("medications", test_data_dir_ + "e2e_medications.csv");
        loadCSVData("procedures", test_data_dir_ + "e2e_procedures.csv");
        loadCSVData("lab_results", test_data_dir_ + "e2e_lab_results.csv");
        loadCSVData("clinical_observations", test_data_dir_ + "e2e_clinical_observations.csv");
        loadCSVData("deaths", test_data_dir_ + "e2e_deaths.csv");
        loadCSVData("clinical_notes", test_data_dir_ + "e2e_clinical_notes.csv");
    }

    void createTargetTables() {
        auto connection = target_db_manager_->getConnection();
        
        // Create OMOP CDM schema
        connection->execute("CREATE SCHEMA IF NOT EXISTS test_cdm");
        
        // Create core OMOP tables using CDM definitions
        OMOPCDMTableCreator table_creator(connection);
        table_creator.createPersonTable("test_cdm");
        table_creator.createObservationPeriodTable("test_cdm");
        table_creator.createVisitOccurrenceTable("test_cdm");
        table_creator.createConditionOccurrenceTable("test_cdm");
        table_creator.createDrugExposureTable("test_cdm");
        table_creator.createProcedureOccurrenceTable("test_cdm");
        table_creator.createMeasurementTable("test_cdm");
        table_creator.createObservationTable("test_cdm");
        table_creator.createDeathTable("test_cdm");
        table_creator.createNoteTable("test_cdm");
    }

    void loadCSVData(const std::string& table_name, const std::string& csv_file) {
        auto connection = source_db_manager_->getConnection();
        
        std::ifstream file(csv_file);
        ASSERT_TRUE(file.is_open()) << "Failed to open CSV file: " << csv_file;
        
        std::string line;
        bool first_line = true;
        std::vector<std::string> columns;
        
        while (std::getline(file, line)) {
            if (first_line) {
                columns = parseCSVLine(line);
                first_line = false;
                continue;
            }
            
            auto values = parseCSVLine(line);
            if (values.size() != columns.size()) {
                continue; // Skip malformed lines
            }
            
            // Build INSERT statement
            std::string sql = "INSERT INTO " + table_name + " (";
            for (size_t i = 0; i < columns.size(); ++i) {
                if (i > 0) sql += ", ";
                sql += columns[i];
            }
            sql += ") VALUES (";
            for (size_t i = 0; i < values.size(); ++i) {
                if (i > 0) sql += ", ";
                if (values[i].empty() || values[i] == "NULL") {
                    sql += "NULL";
                } else {
                    sql += "'" + values[i] + "'";
                }
            }
            sql += ")";
            
            try {
                connection->execute(sql);
            } catch (const std::exception& e) {
                // Log but continue with other records
                Logger::getInstance().warn("Failed to insert record: " + std::string(e.what()));
            }
        }
    }

    std::vector<std::string> parseCSVLine(const std::string& line) {
        std::vector<std::string> result;
        std::string current;
        bool in_quotes = false;
        
        for (char c : line) {
            if (c == '"') {
                in_quotes = !in_quotes;
            } else if (c == ',' && !in_quotes) {
                result.push_back(current);
                current.clear();
            } else {
                current += c;
            }
        }
        result.push_back(current);
        return result;
    }

    void cleanupTestDatabases() {
        if (source_db_manager_) {
            auto connection = source_db_manager_->getConnection();
            connection->execute("DROP SCHEMA IF EXISTS public CASCADE");
            connection->execute("CREATE SCHEMA public");
        }
        
        if (target_db_manager_) {
            auto connection = target_db_manager_->getConnection();
            connection->execute("DROP SCHEMA IF EXISTS test_cdm CASCADE");
        }
    }

    // Test data and configuration
    std::string config_path_;
    std::string test_data_dir_;
    
    // Database configurations
    DatabaseConfig source_db_config_;
    DatabaseConfig target_db_config_;
    
    // Components
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<ETLPipeline> pipeline_;
    std::unique_ptr<DatabaseManager> source_db_manager_;
    std::unique_ptr<DatabaseManager> target_db_manager_;
};

// Test comprehensive ETL pipeline execution
TEST_F(ComprehensiveOMOPETLTest, FullPipelineExecution) {
    ASSERT_NE(pipeline_, nullptr);

    // Execute the complete ETL pipeline
    auto start_time = std::chrono::high_resolution_clock::now();

    bool success = pipeline_->execute();

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

    EXPECT_TRUE(success) << "ETL pipeline execution failed";

    Logger::getInstance().info("ETL pipeline completed in " + std::to_string(duration.count()) + " seconds");
}

// Test data validation and quality checks
TEST_F(ComprehensiveOMOPETLTest, DataValidationAndQualityChecks) {
    // Execute ETL pipeline first
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Verify person count matches source
    auto person_count_result = connection->query(
        "SELECT COUNT(*) as target_count FROM test_cdm.person"
    );
    auto source_count_result = source_db_manager_->getConnection()->query(
        "SELECT COUNT(*) as source_count FROM patients WHERE birth_date IS NOT NULL AND gender IS NOT NULL"
    );

    EXPECT_GT(person_count_result->getInt("target_count"), 0) << "No persons loaded";
    EXPECT_EQ(person_count_result->getInt("target_count"),
              source_count_result->getInt("source_count")) << "Person count mismatch";

    // Test 2: Check for orphan visits
    auto orphan_visits_result = connection->query(R"(
        SELECT COUNT(*) as orphan_count
        FROM test_cdm.visit_occurrence v
        LEFT JOIN test_cdm.person p ON v.person_id = p.person_id
        WHERE p.person_id IS NULL
    )");
    EXPECT_EQ(orphan_visits_result->getInt("orphan_count"), 0) << "Found orphan visits";

    // Test 3: Verify date consistency
    auto date_consistency_result = connection->query(R"(
        SELECT COUNT(*) as invalid_dates
        FROM test_cdm.visit_occurrence
        WHERE visit_start_date > visit_end_date
    )");
    EXPECT_EQ(date_consistency_result->getInt("invalid_dates"), 0) << "Found invalid date ranges";

    // Test 4: Birth date validation
    auto birth_date_result = connection->query(R"(
        SELECT COUNT(*) as invalid_birth_dates
        FROM test_cdm.person
        WHERE year_of_birth < 1900 OR year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE)
    )");
    EXPECT_LE(birth_date_result->getInt("invalid_birth_dates"), 5) << "Too many invalid birth dates";

    // Test 5: Referential integrity check
    auto referential_integrity_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence co
           LEFT JOIN test_cdm.person p ON co.person_id = p.person_id
           WHERE p.person_id IS NULL) +
          (SELECT COUNT(*) FROM test_cdm.drug_exposure de
           LEFT JOIN test_cdm.person p ON de.person_id = p.person_id
           WHERE p.person_id IS NULL) +
          (SELECT COUNT(*) FROM test_cdm.procedure_occurrence po
           LEFT JOIN test_cdm.person p ON po.person_id = p.person_id
           WHERE p.person_id IS NULL) as orphan_records
    )");
    EXPECT_EQ(referential_integrity_result->getInt("orphan_records"), 0) << "Referential integrity violations found";
}

// Test vocabulary mapping coverage
TEST_F(ComprehensiveOMOPETLTest, VocabularyMappingCoverage) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test condition concept mapping coverage
    auto condition_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_conditions,
          COUNT(CASE WHEN condition_concept_id > 0 THEN 1 END) as mapped_conditions,
          ROUND(
            COUNT(CASE WHEN condition_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.condition_occurrence
    )");

    EXPECT_GT(condition_mapping_result->getInt("total_conditions"), 0) << "No conditions loaded";
    EXPECT_GE(condition_mapping_result->getDouble("mapping_percentage"), 80.0)
        << "Condition mapping coverage below 80%";

    // Test drug concept mapping coverage
    auto drug_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_drugs,
          COUNT(CASE WHEN drug_concept_id > 0 THEN 1 END) as mapped_drugs,
          ROUND(
            COUNT(CASE WHEN drug_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.drug_exposure
    )");

    EXPECT_GT(drug_mapping_result->getInt("total_drugs"), 0) << "No drugs loaded";
    EXPECT_GE(drug_mapping_result->getDouble("mapping_percentage"), 75.0)
        << "Drug mapping coverage below 75%";

    // Test procedure concept mapping coverage
    auto procedure_mapping_result = connection->query(R"(
        SELECT
          COUNT(*) as total_procedures,
          COUNT(CASE WHEN procedure_concept_id > 0 THEN 1 END) as mapped_procedures,
          ROUND(
            COUNT(CASE WHEN procedure_concept_id > 0 THEN 1 END) * 100.0 /
            NULLIF(COUNT(*), 0), 2
          ) as mapping_percentage
        FROM test_cdm.procedure_occurrence
    )");

    EXPECT_GT(procedure_mapping_result->getInt("total_procedures"), 0) << "No procedures loaded";
    EXPECT_GE(procedure_mapping_result->getDouble("mapping_percentage"), 70.0)
        << "Procedure mapping coverage below 70%";
}

// Test data transformation accuracy
TEST_F(ComprehensiveOMOPETLTest, DataTransformationAccuracy) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto source_connection = source_db_manager_->getConnection();
    auto target_connection = target_db_manager_->getConnection();

    // Test 1: Verify gender mapping
    auto gender_mapping_result = target_connection->query(R"(
        SELECT
          gender_concept_id,
          COUNT(*) as count
        FROM test_cdm.person
        GROUP BY gender_concept_id
        ORDER BY gender_concept_id
    )");

    // Should have valid gender concept IDs (8507 for Male, 8532 for Female)
    bool found_male = false, found_female = false;
    while (gender_mapping_result->next()) {
        int gender_concept_id = gender_mapping_result->getInt("gender_concept_id");
        if (gender_concept_id == 8507) found_male = true;
        if (gender_concept_id == 8532) found_female = true;
    }
    EXPECT_TRUE(found_male || found_female) << "No valid gender mappings found";

    // Test 2: Verify date transformations
    auto date_transform_result = target_connection->query(R"(
        SELECT
          COUNT(*) as total_visits,
          COUNT(CASE WHEN visit_start_date IS NOT NULL THEN 1 END) as valid_start_dates,
          COUNT(CASE WHEN visit_end_date IS NOT NULL THEN 1 END) as valid_end_dates
        FROM test_cdm.visit_occurrence
    )");

    EXPECT_GT(date_transform_result->getInt("total_visits"), 0) << "No visits loaded";
    EXPECT_EQ(date_transform_result->getInt("valid_start_dates"),
              date_transform_result->getInt("total_visits")) << "Missing visit start dates";

    // Test 3: Verify numeric transformations in measurements
    auto numeric_transform_result = target_connection->query(R"(
        SELECT
          COUNT(*) as total_measurements,
          COUNT(CASE WHEN value_as_number IS NOT NULL THEN 1 END) as numeric_values,
          AVG(value_as_number) as avg_value,
          MIN(value_as_number) as min_value,
          MAX(value_as_number) as max_value
        FROM test_cdm.measurement
        WHERE value_as_number IS NOT NULL
    )");

    if (numeric_transform_result->getInt("total_measurements") > 0) {
        EXPECT_GT(numeric_transform_result->getInt("numeric_values"), 0) << "No numeric measurements";
        EXPECT_GT(numeric_transform_result->getDouble("avg_value"), 0) << "Invalid average measurement value";
    }
}

// Test error handling and data quality scenarios
TEST_F(ComprehensiveOMOPETLTest, ErrorHandlingAndDataQuality) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Check for duplicate records
    auto duplicate_check_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) - COUNT(DISTINCT person_id) FROM test_cdm.person) +
          (SELECT COUNT(*) - COUNT(DISTINCT visit_occurrence_id) FROM test_cdm.visit_occurrence) +
          (SELECT COUNT(*) - COUNT(DISTINCT condition_occurrence_id) FROM test_cdm.condition_occurrence) as duplicate_count
    )");
    EXPECT_EQ(duplicate_check_result->getInt("duplicate_count"), 0) << "Duplicate records found";

    // Test 2: Verify measurement range validation
    auto measurement_range_result = connection->query(R"(
        SELECT COUNT(*) as out_of_range_measurements
        FROM test_cdm.measurement
        WHERE (range_low IS NOT NULL AND value_as_number < range_low * 0.1)
           OR (range_high IS NOT NULL AND value_as_number > range_high * 10)
    )");
    EXPECT_LE(measurement_range_result->getInt("out_of_range_measurements"), 10)
        << "Too many out-of-range measurements";

    // Test 3: Drug exposure validation
    auto drug_validation_result = connection->query(R"(
        SELECT COUNT(*) as invalid_drugs
        FROM test_cdm.drug_exposure
        WHERE drug_exposure_start_date > drug_exposure_end_date
           OR quantity <= 0
           OR days_supply <= 0
    )");
    EXPECT_EQ(drug_validation_result->getInt("invalid_drugs"), 0) << "Invalid drug exposures found";

    // Test 4: Check for reasonable age distributions
    auto age_distribution_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN year_of_birth BETWEEN 1920 AND 2020 THEN 1 END) as reasonable_ages,
          AVG(EXTRACT(YEAR FROM CURRENT_DATE) - year_of_birth) as avg_age
        FROM test_cdm.person
    )");

    EXPECT_GT(age_distribution_result->getInt("total_persons"), 0) << "No persons loaded";
    EXPECT_GE(age_distribution_result->getDouble("reasonable_ages") /
              age_distribution_result->getDouble("total_persons"), 0.9)
        << "Too many unreasonable ages";
    EXPECT_BETWEEN(age_distribution_result->getDouble("avg_age"), 20.0, 80.0)
        << "Average age outside reasonable range";
}

// Test performance and scalability
TEST_F(ComprehensiveOMOPETLTest, PerformanceAndScalability) {
    auto start_time = std::chrono::high_resolution_clock::now();

    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // Performance expectations (adjust based on test data size)
    EXPECT_LT(duration.count(), 60000) << "ETL pipeline took longer than 60 seconds";

    auto connection = target_db_manager_->getConnection();

    // Test record counts meet expectations
    auto record_counts_result = connection->query(R"(
        SELECT
          (SELECT COUNT(*) FROM test_cdm.person) as person_count,
          (SELECT COUNT(*) FROM test_cdm.visit_occurrence) as visit_count,
          (SELECT COUNT(*) FROM test_cdm.condition_occurrence) as condition_count,
          (SELECT COUNT(*) FROM test_cdm.drug_exposure) as drug_count,
          (SELECT COUNT(*) FROM test_cdm.procedure_occurrence) as procedure_count,
          (SELECT COUNT(*) FROM test_cdm.measurement) as measurement_count,
          (SELECT COUNT(*) FROM test_cdm.observation) as observation_count
    )");

    // Verify minimum record counts (adjust based on test data)
    EXPECT_GE(record_counts_result->getInt("person_count"), 50) << "Insufficient person records";
    EXPECT_GE(record_counts_result->getInt("visit_count"), 50) << "Insufficient visit records";
    EXPECT_GE(record_counts_result->getInt("condition_count"), 50) << "Insufficient condition records";
    EXPECT_GE(record_counts_result->getInt("drug_count"), 40) << "Insufficient drug records";
    EXPECT_GE(record_counts_result->getInt("procedure_count"), 60) << "Insufficient procedure records";
    EXPECT_GE(record_counts_result->getInt("measurement_count"), 60) << "Insufficient measurement records";
    EXPECT_GE(record_counts_result->getInt("observation_count"), 60) << "Insufficient observation records";

    Logger::getInstance().info("Performance test completed in " +
                              std::to_string(duration.count()) + " milliseconds");
}

// Test specific OMOP CDM compliance
TEST_F(ComprehensiveOMOPETLTest, OMOPCDMCompliance) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Verify required fields are populated
    auto required_fields_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_ids,
          COUNT(CASE WHEN gender_concept_id IS NOT NULL THEN 1 END) as valid_genders,
          COUNT(CASE WHEN year_of_birth IS NOT NULL THEN 1 END) as valid_birth_years
        FROM test_cdm.person
    )");

    EXPECT_EQ(required_fields_result->getInt("valid_person_ids"),
              required_fields_result->getInt("total_persons")) << "Missing person IDs";
    EXPECT_EQ(required_fields_result->getInt("valid_genders"),
              required_fields_result->getInt("total_persons")) << "Missing gender concepts";
    EXPECT_EQ(required_fields_result->getInt("valid_birth_years"),
              required_fields_result->getInt("total_persons")) << "Missing birth years";

    // Test 2: Verify visit occurrence requirements
    auto visit_requirements_result = connection->query(R"(
        SELECT
          COUNT(*) as total_visits,
          COUNT(CASE WHEN visit_occurrence_id IS NOT NULL THEN 1 END) as valid_visit_ids,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_refs,
          COUNT(CASE WHEN visit_concept_id IS NOT NULL THEN 1 END) as valid_visit_concepts,
          COUNT(CASE WHEN visit_start_date IS NOT NULL THEN 1 END) as valid_start_dates
        FROM test_cdm.visit_occurrence
    )");

    EXPECT_EQ(visit_requirements_result->getInt("valid_visit_ids"),
              visit_requirements_result->getInt("total_visits")) << "Missing visit IDs";
    EXPECT_EQ(visit_requirements_result->getInt("valid_person_refs"),
              visit_requirements_result->getInt("total_visits")) << "Missing person references";
    EXPECT_EQ(visit_requirements_result->getInt("valid_start_dates"),
              visit_requirements_result->getInt("total_visits")) << "Missing visit start dates";

    // Test 3: Verify condition occurrence requirements
    auto condition_requirements_result = connection->query(R"(
        SELECT
          COUNT(*) as total_conditions,
          COUNT(CASE WHEN condition_occurrence_id IS NOT NULL THEN 1 END) as valid_condition_ids,
          COUNT(CASE WHEN person_id IS NOT NULL THEN 1 END) as valid_person_refs,
          COUNT(CASE WHEN condition_start_date IS NOT NULL THEN 1 END) as valid_start_dates
        FROM test_cdm.condition_occurrence
    )");

    EXPECT_EQ(condition_requirements_result->getInt("valid_condition_ids"),
              condition_requirements_result->getInt("total_conditions")) << "Missing condition IDs";
    EXPECT_EQ(condition_requirements_result->getInt("valid_person_refs"),
              condition_requirements_result->getInt("total_conditions")) << "Missing person references";
    EXPECT_EQ(condition_requirements_result->getInt("valid_start_dates"),
              condition_requirements_result->getInt("total_conditions")) << "Missing condition start dates";
}

// Test edge cases and boundary conditions
TEST_F(ComprehensiveOMOPETLTest, EdgeCasesAndBoundaryConditions) {
    ASSERT_TRUE(pipeline_->execute()) << "ETL pipeline execution failed";

    auto connection = target_db_manager_->getConnection();

    // Test 1: Handle NULL and empty values appropriately
    auto null_handling_result = connection->query(R"(
        SELECT
          COUNT(*) as total_persons,
          COUNT(CASE WHEN race_concept_id = 0 THEN 1 END) as unmapped_race,
          COUNT(CASE WHEN ethnicity_concept_id = 0 THEN 1 END) as unmapped_ethnicity
        FROM test_cdm.person
    )");

    // Allow some unmapped values but not all
    EXPECT_LT(null_handling_result->getDouble("unmapped_race") /
              null_handling_result->getDouble("total_persons"), 0.5)
        << "Too many unmapped race values";

    // Test 2: Verify handling of extreme dates
    auto extreme_dates_result = connection->query(R"(
        SELECT
          COUNT(*) as future_visits,
          COUNT(*) as very_old_visits
        FROM test_cdm.visit_occurrence
        WHERE visit_start_date > CURRENT_DATE + INTERVAL '1 year'
           OR visit_start_date < DATE '1900-01-01'
    )");

    EXPECT_EQ(extreme_dates_result->getInt("future_visits"), 0) << "Found future visit dates";
    EXPECT_LE(extreme_dates_result->getInt("very_old_visits"), 5) << "Too many very old visit dates";

    // Test 3: Verify measurement value ranges
    auto measurement_extremes_result = connection->query(R"(
        SELECT
          COUNT(*) as total_measurements,
          COUNT(CASE WHEN value_as_number < 0 AND measurement_concept_id IN (3025315, 3012888) THEN 1 END) as negative_vitals,
          COUNT(CASE WHEN value_as_number > 1000 AND measurement_concept_id IN (3025315, 3012888) THEN 1 END) as extreme_vitals
        FROM test_cdm.measurement
        WHERE value_as_number IS NOT NULL
    )");

    // Should have minimal negative vital signs or extreme values
    EXPECT_LE(measurement_extremes_result->getInt("negative_vitals"), 2)
        << "Too many negative vital signs";
    EXPECT_LE(measurement_extremes_result->getInt("extreme_vitals"), 5)
        << "Too many extreme vital sign values";
}

} // namespace test
} // namespace omop_etl
