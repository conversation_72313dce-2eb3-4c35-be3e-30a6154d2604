# Updated E2E integration tests CMakeLists.txt
set(E2E_INTEGRATION_TEST_SOURCES
    test_e2e_integration_updated.cpp
)

# Comprehensive OMOP ETL E2E test sources
set(COMPREHENSIVE_E2E_TEST_SOURCES
    test_comprehensive_omop_etl.cpp
)

add_executable(e2e_integration_tests_updated ${E2E_INTEGRATION_TEST_SOURCES})

# Create comprehensive OMOP ETL E2E test executable
add_executable(comprehensive_omop_etl_e2e_tests ${COMPREHENSIVE_E2E_TEST_SOURCES})

target_link_libraries(e2e_integration_tests_updated
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        integration_test_helpers
        gtest
        gtest_main
        gmock
        nlohmann_json::nlohmann_json
        Threads::Threads
)

target_link_libraries(comprehensive_omop_etl_e2e_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_etl_pipeline
        omop_config_manager
        omop_database_manager
        omop_vocabulary
        integration_test_helpers
        gtest
        gtest_main
        gmock
        nlohmann_json::nlohmann_json
        yaml-cpp
        PostgreSQL::PostgreSQL
        Threads::Threads
)

target_include_directories(e2e_integration_tests_updated
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${CMAKE_CURRENT_SOURCE_DIR}/../test_helpers
)

target_include_directories(comprehensive_omop_etl_e2e_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/etl
        ${CMAKE_SOURCE_DIR}/src/lib/database
        ${CMAKE_SOURCE_DIR}/src/lib/cdm
        ${CMAKE_SOURCE_DIR}/src/lib/utils
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${CMAKE_CURRENT_SOURCE_DIR}/../test_helpers
)

# Add compile definitions for test configuration
target_compile_definitions(e2e_integration_tests_updated
    PRIVATE
        TEST_DATA_DIR="${CMAKE_CURRENT_SOURCE_DIR}/../test_data"
        TEST_OUTPUT_DIR="${CMAKE_CURRENT_BINARY_DIR}/test_output"
        OMOP_TEST_INTEGRATION
        OMOP_E2E_TESTS
        OMOP_CDM_VERSION="6.0"
)

target_compile_definitions(comprehensive_omop_etl_e2e_tests
    PRIVATE
        TEST_DATA_DIR="${CMAKE_CURRENT_SOURCE_DIR}/../test_data"
        TEST_OUTPUT_DIR="${CMAKE_CURRENT_BINARY_DIR}/test_output"
        OMOP_TEST_INTEGRATION
        OMOP_E2E_TESTS
        OMOP_COMPREHENSIVE_TESTS
        OMOP_CDM_VERSION="6.0"
        OMOP_TEST_DATABASE_PREFIX="omop_etl_test"
)

add_test(
    NAME e2e_integration_tests_updated
    COMMAND e2e_integration_tests_updated
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

# Add comprehensive OMOP ETL E2E test
add_test(
    NAME comprehensive_omop_etl_e2e_tests
    COMMAND comprehensive_omop_etl_e2e_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

set_tests_properties(e2e_integration_tests_updated PROPERTIES
    TIMEOUT 1800  # 30 minutes for comprehensive E2E tests
    LABELS "integration;e2e;updated;comprehensive"
    ENVIRONMENT "TEST_DATA_DIR=${CMAKE_CURRENT_SOURCE_DIR}/../test_data;TEST_OUTPUT_DIR=${CMAKE_CURRENT_BINARY_DIR}/test_output"
)

set_tests_properties(comprehensive_omop_etl_e2e_tests PROPERTIES
    TIMEOUT 3600  # 60 minutes for comprehensive OMOP ETL tests
    LABELS "integration;e2e;comprehensive;omop;etl;pipeline"
    ENVIRONMENT "TEST_DATA_DIR=${CMAKE_CURRENT_SOURCE_DIR}/../test_data;TEST_OUTPUT_DIR=${CMAKE_CURRENT_BINARY_DIR}/test_output"
    DEPENDS "vocabulary_tests;cdm_tests;etl_tests"  # Run after core component tests
)

# Create test output directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output)

# Create test data output directories for comprehensive tests
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output/comprehensive_e2e)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output/comprehensive_e2e/logs)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output/comprehensive_e2e/reports)