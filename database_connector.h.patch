--- a/src/lib/extract/database_connector.h
+++ b/src/lib/extract/database_connector.h
@@ -65,6 +65,13 @@ public:
      */
     virtual std::string column_type(size_t index) const = 0;
 
+    /**
+     * @brief Get all column names
+     * @return std::vector<std::string> Column names
+     */
+    virtual std::vector<std::string> get_column_names() const = 0;
+
+
     /**
      * @brief Convert current row to Record
      * @return core::Record Record representation of current row
@@ -82,6 +89,18 @@ public:
         return record;
     }
 };
+
+    /**
+     * @brief Get all column names (implementation for derived classes)
+     * @return std::vector<std::string> Column names
+     */
+    std::vector<std::string> get_column_names() const override {
+        std::vector<std::string> names;
+        for (size_t i = 0; i < column_count(); ++i) {
+            names.push_back(column_name(i));
+        }
+        return names;
+    }
 };
 
 /**
@@ -269,6 +288,9 @@ protected:
     size_t total_extracted_{0};
     size_t batch_count_{0};
     std::chrono::steady_clock::time_point start_time_;
+    size_t limit_{0};  ///< Query limit (0 = no limit)
+
+    std::chrono::milliseconds get_processing_time() const;
 };
 
 /**
@@ -323,6 +345,7 @@ public:
         size_t total_releases;
         size_t wait_count;
         std::chrono::milliseconds avg_wait_time;
+        int64_t total_wait_time_ms;
     };
 
     [[nodiscard]] PoolStats get_statistics() const;
@@ -351,10 +374,10 @@ private:
     size_t active_connections_{0};
     bool shutdown_{false};
     
-    // Statistics
+    // Statistics - reorder to match initialization order
     std::atomic<size_t> total_acquisitions_;
     std::atomic<size_t> total_releases_;
     std::atomic<size_t> wait_count_;
+    std::atomic<size_t> active_connections_;
     std::atomic<int64_t> total_wait_time_; // milliseconds
 };